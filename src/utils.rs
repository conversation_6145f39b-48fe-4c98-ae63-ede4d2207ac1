//! 加密和证书相关的实用工具
//!
//! 提供证书加载、密钥管理等安全相关的功能

use bytes::Bytes;
use crossbeam::queue::SegQueue;
use rustls::pki_types::pem::PemObject;
use rustls::pki_types::{CertificateDer, PrivateKeyDer};
use std::io::Error;
use std::sync::{Arc, LazyLock};
use std::time::Duration;
use tokio::io::{AsyncReadExt, AsyncWriteExt};
use tokio::net::TcpStream;
use tokio_rustls::TlsConnector;
use tracing::info;

/// 加载 PEM 格式的证书文件
///
/// # 参数
///
/// * `path` - 证书文件路径
///
/// # 返回
///
/// 返回证书列表，通常包含服务器证书和中间证书
///
/// # 示例
///
/// ```ignore
/// use boa::utils::crypto::load_certs;
///
/// let certs = load_certs("server.crt".to_string())?;
/// println!("加载了 {} 个证书", certs.len());
/// ```
pub fn load_certs(path: String) -> Result<Vec<CertificateDer<'static>>, Error> {
    let certs = CertificateDer::pem_file_iter(path)
        .map_err(|e| Error::other(format!("加载证书失败: {}", e)))?;
    let cert_vec = certs
        .collect::<Result<Vec<_>, _>>()
        .map_err(|e| Error::other(format!("解析证书失败: {}", e)))?;
    Ok(cert_vec)
}

/// 加载 PEM 格式的私钥文件
///
/// # 参数
///
/// * `path` - 私钥文件路径
///
/// # 返回
///
/// 返回私钥，支持 RSA、ECDSA 等格式
///
/// # 示例
///
/// ```ignore
/// use boa::utils::crypto::load_private_key;
///
/// let key = load_private_key("server.key".to_string())?;
/// println!("成功加载私钥");
/// ```
pub fn load_private_key(path: String) -> Result<PrivateKeyDer<'static>, Error> {
    let key = PrivateKeyDer::from_pem_file(path)
        .map_err(|e| Error::other(format!("加载私钥失败: {}", e)))?;
    Ok(key)
}

/// 创建 Rustls 客户端连接的辅助函数，支持跳过证书验证选项和socket优化，带有5秒超时
pub async fn create_rustls_client_connection_with_options(
    addr: &str,
    server_name: &str,
    skip_verify: bool,
) -> Result<tokio_rustls::client::TlsStream<TcpStream>, Error> {
    create_rustls_client_connection_with_timeout(
        addr,
        server_name,
        skip_verify,
        Duration::from_secs(5),
    )
    .await
}

/// 创建 Rustls 客户端连接的辅助函数，支持跳过证书验证选项、socket优化和自定义超时
pub async fn create_rustls_client_connection_with_timeout(
    addr: &str,
    server_name: &str,
    skip_verify: bool,
    timeout: Duration,
) -> Result<tokio_rustls::client::TlsStream<TcpStream>, Error> {
    let mut root_cert_store = rustls::RootCertStore::empty();
    root_cert_store.extend(webpki_roots::TLS_SERVER_ROOTS.iter().cloned());

    let config_builder = if skip_verify {
        rustls::ClientConfig::builder()
            .dangerous()
            .with_custom_certificate_verifier(Arc::new(SkipServerVerification {}))
            .with_no_client_auth()
    } else {
        rustls::ClientConfig::builder()
            .with_root_certificates(root_cert_store)
            .with_no_client_auth()
    };

    let connector = TlsConnector::from(Arc::new(config_builder));

    // 从 server_name 中提取域名部分（移除端口号）
    let hostname = if let Some(colon_pos) = server_name.find(':') {
        &server_name[..colon_pos]
    } else {
        server_name
    };

    let domain = rustls::pki_types::ServerName::try_from(hostname.to_string())
        .map_err(|e| Error::other(format!("Invalid server name '{}': {}", hostname, e)))?;

    // 设置超时进行TLS连接
    let tls_stream = tokio::time::timeout(timeout, async {
        let tcp_stream = TcpStream::connect(addr).await?;
        connector.connect(domain, tcp_stream).await
    })
    .await
    .map_err(|_| {
        Error::other(format!(
            "TLS connection to {} timed out after {:?}",
            addr, timeout
        ))
    })?
    .map_err(|e| Error::other(format!("TLS connection failed: {}", e)))?;

    info!(
        "Optimized TLS connection established to {} (SNI: {}) within timeout",
        addr, hostname
    );
    Ok(tls_stream)
}

#[derive(Debug)]
struct SkipServerVerification;

impl rustls::client::danger::ServerCertVerifier for SkipServerVerification {
    fn verify_server_cert(
        &self,
        _end_entity: &CertificateDer,
        _intermediates: &[CertificateDer],
        _server_name: &rustls::pki_types::ServerName,
        _ocsp_response: &[u8],
        _now: rustls::pki_types::UnixTime,
    ) -> Result<rustls::client::danger::ServerCertVerified, rustls::Error> {
        Ok(rustls::client::danger::ServerCertVerified::assertion())
    }

    fn verify_tls12_signature(
        &self,
        _message: &[u8],
        _cert: &CertificateDer,
        _dss: &rustls::DigitallySignedStruct,
    ) -> Result<rustls::client::danger::HandshakeSignatureValid, rustls::Error> {
        Ok(rustls::client::danger::HandshakeSignatureValid::assertion())
    }

    fn verify_tls13_signature(
        &self,
        _message: &[u8],
        _cert: &CertificateDer,
        _dss: &rustls::DigitallySignedStruct,
    ) -> Result<rustls::client::danger::HandshakeSignatureValid, rustls::Error> {
        Ok(rustls::client::danger::HandshakeSignatureValid::assertion())
    }

    fn supported_verify_schemes(&self) -> Vec<rustls::SignatureScheme> {
        vec![
            rustls::SignatureScheme::RSA_PKCS1_SHA1,
            rustls::SignatureScheme::ECDSA_SHA1_Legacy,
            rustls::SignatureScheme::RSA_PKCS1_SHA256,
            rustls::SignatureScheme::ECDSA_NISTP256_SHA256,
            rustls::SignatureScheme::RSA_PKCS1_SHA384,
            rustls::SignatureScheme::ECDSA_NISTP384_SHA384,
            rustls::SignatureScheme::RSA_PKCS1_SHA512,
            rustls::SignatureScheme::ECDSA_NISTP521_SHA512,
            rustls::SignatureScheme::RSA_PSS_SHA256,
            rustls::SignatureScheme::RSA_PSS_SHA384,
            rustls::SignatureScheme::RSA_PSS_SHA512,
            rustls::SignatureScheme::ED25519,
            rustls::SignatureScheme::ED448,
        ]
    }
}

pub struct BufferPool {
    // BytesMut 池 - 这是唯一需要的池！
    bytesmut_pools: [SegQueue<bytes::BytesMut>; 11],

}

impl BufferPool {
    // 11 个缓冲区大小级别，模仿 sing-box
    const SIZES: [usize; 11] = [
        64,    // 64B
        128,   // 128B
        256,   // 256B
        512,   // 512B
        1024,  // 1KB
        2048,  // 2KB
        4096,  // 4KB
        8192,  // 8KB
        16384, // 16KB
        32768, // 32KB
        65536, // 64KB
    ];

    pub fn new() -> Self {
      

        Self {
            bytesmut_pools: [
                SegQueue::new(),
                SegQueue::new(),
                SegQueue::new(),
                SegQueue::new(),
                SegQueue::new(),
                SegQueue::new(),
                SegQueue::new(),
                SegQueue::new(),
                SegQueue::new(),
                SegQueue::new(),
                SegQueue::new(),
            ],
        }
    }

    /// 找到最适合的缓冲区大小索引
    fn find_pool_index(&self, size: usize) -> Option<usize> {
        for (i, &pool_size) in Self::SIZES.iter().enumerate() {
            if size <= pool_size {
                return Some(i);
            }
        }
        None // 超过最大大小
    }

    pub fn get_bytesmut(&self, size: usize) -> bytes::BytesMut {
        if let Some(pool_idx) = self.find_pool_index(size) {
            let pool_size = Self::SIZES[pool_idx];

            // 尝试从对应的池中获取 BytesMut
            if let Some(mut buf) = self.bytesmut_pools[pool_idx].pop() {
                buf.clear();
                buf
            } else {
                // 池为空，创建新的 BytesMut
                bytes::BytesMut::with_capacity(pool_size)
            }
        } else {
            // 超过最大池大小，直接分配
            bytes::BytesMut::with_capacity(size)
        }
    }

    /// 归还 BytesMut 到池中（完全无锁操作，带池大小限制）
    pub fn return_bytesmut(&self, mut buf: bytes::BytesMut) {
        let capacity = buf.capacity();

        // 找到合适的池
        if let Some(pool_idx) = self.find_pool_index(capacity) {
            let pool_size = Self::SIZES[pool_idx];

            // 只有容量匹配或接近的缓冲区才归还到池中
            if capacity <= pool_size * 2 {
                buf.clear();
                self.bytesmut_pools[pool_idx].push(buf);
                return;
            }
        }

        // 池已满或不合适的缓冲区直接丢弃，让 GC 回收
    }

    /// 创建一个可重用的 BytesMut 包装器 - 关键优化！
    pub fn get_reusable_bytesmut(&self, size: usize) -> ReusableBytesMut {
        let buf = self.get_bytesmut(size);
        ReusableBytesMut {
            buffer: buf,
            pool: self,
        }
    }
}

/// 可重用的 BytesMut 包装器，会在 drop 时自动归还到池中 - 关键优化！
pub struct ReusableBytesMut<'a> {
    buffer: bytes::BytesMut,
    pool: &'a BufferPool,
}

impl<'a> ReusableBytesMut<'a> {
    /// 获取 BytesMut 的可变引用
    pub fn as_mut(&mut self) -> &mut bytes::BytesMut {
        &mut self.buffer
    }

    /// 获取 BytesMut 的不可变引用
    pub fn as_ref(&self) -> &bytes::BytesMut {
        &self.buffer
    }

    /// 使用 from_owner 实现零拷贝的 freeze 方法
    /// 注意：这会消费掉 ReusableBytesMut，缓冲区会在 Bytes 被 drop 时自动归还到池中
    pub fn freeze_zero_copy(mut self) -> Bytes {
        if self.buffer.is_empty() {
            return Bytes::new();
        }

        // 取出 BytesMut，避免自动归还到池中
        let buffer = std::mem::take(&mut self.buffer);
        
        // 防止 Drop 被调用（因为我们已经取出了 buffer）
        std::mem::forget(self);

        // 创建自定义 Owner 结构来持有 BytesMut
        // 只引用全局静态池，避免生命周期问题
        struct BytesMutOwner {
            buffer: bytes::BytesMut,
        }

        impl AsRef<[u8]> for BytesMutOwner {
            fn as_ref(&self) -> &[u8] {
                &self.buffer
            }
        }

        impl Drop for BytesMutOwner {
            fn drop(&mut self) {
                // 当 Bytes 被 drop 时，将缓冲区归还到全局池中
                let buf = std::mem::take(&mut self.buffer);
                GLOBAL_BUFFER_POOL.return_bytesmut(buf);
            }
        }

        let owner = BytesMutOwner { buffer };
        Bytes::from_owner(owner)
    }
}

impl<'a> Drop for ReusableBytesMut<'a> {
    fn drop(&mut self) {
        // 自动归还缓冲区到池中
        let buf = std::mem::take(&mut self.buffer);
        self.pool.return_bytesmut(buf);
    }
}

// 移除 Deref 实现，避免暴露完整的 BytesMut API
// 只提供安全的方法来访问数据

impl<'a> ReusableBytesMut<'a> {
    /// 获取数据的不可变切片
    pub fn as_slice(&self) -> &[u8] {
        &self.buffer
    }

    /// 获取数据的可变切片
    pub fn as_mut_slice(&mut self) -> &mut [u8] {
        &mut self.buffer
    }

    /// 获取当前长度
    pub fn len(&self) -> usize {
        self.buffer.len()
    }

    /// 检查是否为空
    pub fn is_empty(&self) -> bool {
        self.buffer.is_empty()
    }

    /// 清空缓冲区
    pub fn clear(&mut self) {
        self.buffer.clear();
    }

    /// 扩展数据（从切片）
    pub fn extend_from_slice(&mut self, src: &[u8]) {
        self.buffer.extend_from_slice(src);
    }

    /// 预留容量
    pub fn reserve(&mut self, additional: usize) {
        self.buffer.reserve(additional);
    }

    /// 获取内部 BytesMut 的可变引用（仅用于需要 BufMut trait 的场景）
    /// 注意：这会暴露完整的 BytesMut API，使用时需要小心
    pub fn as_mut_bytesmut(&mut self) -> &mut bytes::BytesMut {
        &mut self.buffer
    }
}

// 全局缓冲区池实例
pub static GLOBAL_BUFFER_POOL: LazyLock<BufferPool> = LazyLock::new(|| BufferPool::new());

/// 更安全的使用缓冲池的双向拷贝函数
///
/// 相比上面的版本，这个版本不使用 unsafe 代码，而是通过 resize 来调整缓冲区大小
pub async fn copy_bidirectional_with_pool_safe<L, R>(
    left: &mut L,
    right: &mut R,
) -> Result<(u64, u64), Error>
where
    L: AsyncReadExt + AsyncWriteExt + Unpin + ?Sized,
    R: AsyncReadExt + AsyncWriteExt + Unpin + ?Sized,
{
    let mut left_to_right = 0u64;
    let mut right_to_left = 0u64;

    // 从池中获取两个缓冲区
    let mut buf1 = [0u8; 8196];
    let mut buf2 = [0u8; 8196];
    loop {
        tokio::select! {
            // 从左到右的数据传输
            result = left.read(&mut buf1[..]) => {
                match result {
                    Ok(0) => break, // EOF
                    Ok(n) => {
                        if let Err(e) = right.write_all(&buf1[..n]).await {
                            return Err(e);
                        }
                        left_to_right += n as u64;
                    }
                    Err(e) => return Err(e),
                }
            }

            // 从右到左的数据传输
            result = right.read(&mut buf2[..]) => {
                match result {
                    Ok(0) => break, // EOF
                    Ok(n) => {
                        if let Err(e) = left.write_all(&buf2[..n]).await {
                            return Err(e);
                        }
                        right_to_left += n as u64;
                    }
                    Err(e) => return Err(e),
                }
            }
        }
    }

    Ok((left_to_right, right_to_left))
}
