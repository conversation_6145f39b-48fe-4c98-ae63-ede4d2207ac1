use bytes::{Buf, BufMut, BytesMut};
use std::collections::HashSet;
use std::io;
use std::net::SocketAddr;
use std::sync::Arc;
use tokio::io::{AsyncReadExt, AsyncWriteExt};
use tokio::net::{TcpListener, TcpStream, UdpSocket};
use tokio::time::Duration;
use tokio_util::sync::CancellationToken;

use crate::SockAddr;
use crate::client::pool::StreamPool;
use crate::utils::copy_bidirectional_with_pool_safe;

/// UDP 缓冲区大小
const UDP_BUF_SIZE: usize = 65_535;

pub struct Socks5Server {
    listener: TcpListener,
    client: Arc<StreamPool>,
    auth: Option<(String, String)>,
}

impl Socks5Server {
    /// 绑定地址并创建服务器
    pub async fn bind(
        addr: &str,
        client: Arc<StreamPool>,
        auth: Option<(String, String)>,
    ) -> io::Result<Self> {
        let listener = TcpListener::bind(addr).await?;
        Ok(Self {
            listener,
            client,
            auth,
        })
    }

    /// 运行服务器，接受并处理连接
    pub async fn run(self) -> io::Result<()> {
        loop {
            let (stream, _) = self.listener.accept().await?;
            let client = self.client.clone();
            let auth = self.auth.clone();
            tokio::spawn(async move {
                if let Err(e) = handle_connection(stream, client, auth).await {
                    tracing::error!("socks5 handle error: {e}");
                }
            });
        }
    }
}

/// 进行握手并根据需要执行用户名密码验证
pub async fn handshake<S>(stream: &mut S, auth: &Option<(String, String)>) -> io::Result<()>
where
    S: AsyncReadExt + AsyncWriteExt + Unpin,
{
    let mut buf = [0u8; 2];
    stream.read_exact(&mut buf).await?;
    if buf[0] != 5 {
        return Err(io::Error::other("Invalid SOCKS version"));
    }
    let nmethods = buf[1] as usize;
    let mut methods = vec![0u8; nmethods];
    stream.read_exact(&mut methods).await?;

    let (method, need_auth) = if auth.is_some() {
        if methods.contains(&2) {
            (2u8, true)
        } else {
            return Err(io::Error::other("Auth method not supported"));
        }
    } else if methods.contains(&0) {
        (0u8, false)
    } else {
        return Err(io::Error::other("No compatible method"));
    };
    stream.write_all(&[5, method]).await?;

    if need_auth {
        // 用户名密码认证 RFC1929
        let mut header = [0u8; 2];
        stream.read_exact(&mut header).await?;
        if header[0] != 1 {
            return Err(io::Error::other("Auth version"));
        }
        let ulen = header[1] as usize;
        let mut uname = vec![0u8; ulen];
        stream.read_exact(&mut uname).await?;
        let mut plen_buf = [0u8; 1];
        stream.read_exact(&mut plen_buf).await?;
        let plen = plen_buf[0] as usize;
        let mut passwd = vec![0u8; plen];
        stream.read_exact(&mut passwd).await?;
        let uname = String::from_utf8(uname).map_err(|_| io::Error::other("utf8"))?;
        let passwd = String::from_utf8(passwd).map_err(|_| io::Error::other("utf8"))?;

        if let Some((u, p)) = auth {
            if &uname == u && &passwd == p {
                stream.write_all(&[1, 0]).await?;
            } else {
                stream.write_all(&[1, 1]).await?;
                return Err(io::Error::new(
                    io::ErrorKind::PermissionDenied,
                    "Bad credentials",
                ));
            }
        }
    }
    Ok(())
}

/// 处理单个 SOCKS5 连接
async fn handle_connection(
    mut socket: TcpStream,
    client: Arc<StreamPool>,
    auth: Option<(String, String)>,
) -> io::Result<()> {
    // 1. 处理握手和可选认证
    handshake(&mut socket, &auth).await?;

    // 2. 读取连接请求
    let mut header = [0u8; 4];
    socket.read_exact(&mut header).await?;
    if header[0] != 5 {
        return Err(io::Error::other("Invalid version"));
    }
    let cmd = header[1];
    let atyp = header[3];

    // 解析目标地址
    let target = match atyp {
        1 => {
            // IPv4
            let mut addr_port = [0u8; 6];
            socket.read_exact(&mut addr_port).await?;
            let ip =
                std::net::Ipv4Addr::new(addr_port[0], addr_port[1], addr_port[2], addr_port[3]);
            let port = u16::from_be_bytes([addr_port[4], addr_port[5]]);
            SockAddr::new(crate::Address::Ipv4(ip), port)
        }
        3 => {
            let mut len = [0u8; 1];
            socket.read_exact(&mut len).await?;
            let len = len[0] as usize;
            let mut domain = vec![0u8; len + 2];
            socket.read_exact(&mut domain).await?;
            let port = u16::from_be_bytes([domain[len], domain[len + 1]]);
            let domain = String::from_utf8(domain[..len].to_vec())
                .map_err(|_| io::Error::other("Invalid domain"))?;
            SockAddr::new(crate::Address::Domain(domain), port)
        }
        4 => {
            // IPv6
            let mut addr_port = [0u8; 18];
            socket.read_exact(&mut addr_port).await?;
            let ip = std::net::Ipv6Addr::from(<[u8; 16]>::try_from(&addr_port[..16]).unwrap());
            let port = u16::from_be_bytes([addr_port[16], addr_port[17]]);
            SockAddr::new(crate::Address::Ipv6(ip), port)
        }
        _ => return Err(io::Error::other("Invalid ATYP")),
    };

    match cmd {
        1 => {
            // 回复成功，但 BND.ADDR 为 0
            socket.write_all(&[5, 0, 0, 1, 0, 0, 0, 0, 0, 0]).await?;

            let mut remote = client
                .get_stream(target.clone())
                .await
                .map_err(|e| io::Error::other(format!("create_stream: {e}")))?;

            let res = copy_bidirectional_with_pool_safe(&mut socket, &mut *remote).await;

            let _ = remote.shutdown().await;
            let _ = socket.shutdown().await;
            res.map(|_| ())
        }
        // UDP ASSOCIATE
        3 => handle_udp(socket, client).await,
        _ => Err(io::Error::other("Unsupported CMD")),
    }
}

/// 处理 UDP ASSOCIATE
async fn handle_udp(mut socket: TcpStream, client: Arc<StreamPool>) -> io::Result<()> {
    // 步骤 1: 创建本地 UDP 套接字并监听
    let udp = UdpSocket::bind("0.0.0.0:0").await?;
    let local_addr = udp.local_addr()?;

    // 步骤 2: 遵循 SOCKS5 协议，将绑定的地址回复给客户端
    let mut resp = vec![5u8, 0, 0]; // Version, RSV, FRAG
    match local_addr {
        SocketAddr::V4(sa) => {
            resp.push(1); // ATYP: IPv4
            resp.extend_from_slice(&sa.ip().octets());
            resp.extend_from_slice(&sa.port().to_be_bytes());
        }
        SocketAddr::V6(sa) => {
            resp.push(4); // ATYP: IPv6
            resp.extend_from_slice(&sa.ip().octets());
            resp.extend_from_slice(&sa.port().to_be_bytes());
        }
    }
    socket.write_all(&resp).await?;

    // 步骤 3: 准备共享资源和取消令牌，用于新的异步任务
    let udp = Arc::new(udp);
    let client_clone = client.clone();
    let udp_clone = udp.clone();

    let cancellation_token = CancellationToken::new();
    let udp_token = cancellation_token.clone();

    // 步骤 4: 启动核心处理任务。这个任务将处理所有双向数据流。
    let udp_task = tokio::spawn(async move {
        // 获取到远端的 UDP-over-TCP 流
        let uot_addr = SockAddr::new(
            crate::Address::Domain("sp.v2.udp-over-tcp.arpa".to_string()),
            0,
        );
        let Ok(stream) = client_clone
            .get_stream(uot_addr.into_socks5_bytes().unwrap())
            .await
            .map_err(|e| io::Error::other(format!("get_stream: {e}")))
        else {
            return;
        };

        let mut is_first_packet = true;

        let (mut read_half, mut write_half) = stream.into_owned_split();

        let mut udp_buf = vec![0u8; UDP_BUF_SIZE];
        let mut stream_buf = vec![0u8; UDP_BUF_SIZE];

        let mut peer_set = HashSet::new();

        loop {
            tokio::select! {
                // 分支 A: 从本地 UDP 端口接收数据，然后通过 `write_half` 发送到远端
                result = udp_clone.recv_from(&mut udp_buf) => {
                    let Ok((n, peer)) = result else {
                        break;
                    };
                    if n < 3 { continue; } // 忽略无效的 SOCKS5 UDP 请求

                    let mut data = BytesMut::from(&udp_buf[..n]);
                    data.advance(3); // 跳过 RSV 和 FRAG
                    let Ok(addr) = SockAddr::from_socks5(&mut data) else {
                        continue;
                    };
                    let payload = data.freeze();
                    if payload.is_empty() { continue; }

                    peer_set.insert(peer);

                    let mut full_packet = BytesMut::new();
                    if is_first_packet {
                        full_packet.put_u8(0); // RSV
                        addr.to_socks5(&mut full_packet).ok();
                        is_first_packet = false;
                    }
                    addr.to_sing_box_uot_socks5(&mut full_packet).ok();
                    full_packet.put_u16(payload.len() as u16);
                    full_packet.extend_from_slice(&payload);

                    // 使用 `write_half` 发送数据，不会与 `read_half` 冲突
                    if let Err(e) = write_half.write_all(&full_packet).await {
                        tracing::error!("write_half.write_all error: {e}");
                        break;
                    }
                }

                // 分支 B: 从远端的 `read_half` 读取数据，然后通过 UDP 发送给所有已知的 peers
                result = read_half.read(&mut stream_buf) => {
                    let len = match result {
                        Ok(0) => {
                            break;
                        },
                        Ok(len) => len,
                        Err(e) => {
                            tracing::error!("read_half.read error: {e}");
                            break; // 连接出错
                        }
                    };

                    if len < 8 { continue; }

                    let mut resp_data = BytesMut::from(&stream_buf[..len]);

                    let Ok(_response_addr) = SockAddr::from_sing_box_uot_socks5(&mut resp_data) else { continue; };
                    if resp_data.remaining() < 2 { continue; }
                    let data_len = resp_data.get_u16() as usize;
                    if resp_data.remaining() < data_len { continue; }
                    let actual_data = resp_data.split_to(data_len);

                    let mut send_buf = BytesMut::new();
                    send_buf.put_u16(0); // RSV
                    send_buf.put_u8(0);  // FRAG
                    match local_addr { // 使用我们自己的地址作为响应源
                        SocketAddr::V4(sa) => {
                            send_buf.put_u8(1);
                            send_buf.put_slice(&sa.ip().octets());
                            send_buf.put_u16(sa.port());
                        }
                        SocketAddr::V6(sa) => {
                            send_buf.put_u8(4);
                            send_buf.put_slice(&sa.ip().octets());
                            send_buf.put_u16(sa.port());
                        }
                    }
                    send_buf.extend_from_slice(&actual_data);

                    // 将响应发送给所有已知的 peer
                    for &peer in &peer_set {
                        let _ = udp_clone.send_to(&send_buf, peer).await;
                    }
                }

                // 分支 C: 监听外部的取消信号
                _ = udp_token.cancelled() => {
                    break;
                }
            }
        }

        // 清理：优雅地关闭写入半部分，这会向远端发送 FIN 包
        let _ = write_half.shutdown().await;
    });

    let mut tmp = [0u8; 1];
    let tcp_result = async {
        while socket.read(&mut tmp).await? != 0 {}
        Ok::<(), io::Error>(())
    }
    .await;

    cancellation_token.cancel();

    let _ = tokio::time::timeout(Duration::from_secs(5), udp_task).await;

    tcp_result
}
