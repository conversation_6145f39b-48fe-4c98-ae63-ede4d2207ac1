use std::time::Duration;

/// Session配置结构
#[derive(Debu<PERSON>, <PERSON><PERSON>)]
pub struct SessionConfig {
    /// 服务器地址 (如: "www.example.com:25565")
    pub server_addr: String,
    /// 服务器名称 (用于TLS SNI, 如: "www.example.com")
    pub server_name: String,
    /// FIN超时时间
    pub fin_timeout: Option<Duration>,
    /// 连接超时
    pub connect_timeout: Option<Duration>,
    /// 是否跳过证书验证
    pub skip_verify: bool,
    /// 密码
    pub password: String,
    /// 最小会话数
    pub min_session_count: usize,
}

impl Default for SessionConfig {
    fn default() -> Self {
        Self {
            server_addr: "localhost:25565".to_string(),
            server_name: "localhost".to_string(),
            fin_timeout: Some(Duration::from_secs(5)),
            connect_timeout: Some(Duration::from_secs(30)),
            skip_verify: true,
            password: String::new(),
            min_session_count: 2,
        }
    }
}

impl SessionConfig {
    /// 创建一个新的SessionConfig
    pub fn new(server_addr: String, server_name: String, password: String, min_session_count: usize) -> Self {
        Self {
            server_addr,
            server_name,
            password,
            min_session_count,
            ..Default::default()
        }
    }

    /// 设置FIN超时
    pub fn with_fin_timeout(mut self, timeout: Duration) -> Self {
        self.fin_timeout = Some(timeout);
        self
    }

    /// 设置连接超时
    pub fn with_connect_timeout(mut self, timeout: Duration) -> Self {
        self.connect_timeout = Some(timeout);
        self
    }

    /// 设置是否跳过证书验证
    pub fn with_skip_verify(mut self, skip_verify: bool) -> Self {
        self.skip_verify = skip_verify;
        self
    }

    /// 设置最小会话数
    pub fn with_min_session_count(mut self, count: usize) -> Self {
        self.min_session_count = count;
        self
    }
}
