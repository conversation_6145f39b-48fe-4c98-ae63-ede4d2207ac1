use super::timeout_manager::{Timeout<PERSON><PERSON>, TimeoutGuard, TimeoutManagerHandle};
use crate::SockAddr;

use crate::protocol::command::Command;
use crate::protocol::frame::{FRAME_HEADER_SIZE, Frame, PooledFrame};
use crate::protocol::padding::{PaddingScheme, PaddingWriter};
use crate::session::{StreamContext, StreamHandle, StreamState};
use bytes::{Buf, Bytes, BytesMut};
use std::collections::HashMap;
use std::sync::{
    Arc,
    atomic::{AtomicU32, AtomicU64, Ordering},
};
use std::time::{Duration, Instant};
use thiserror::Error;
use tokio::io::{AsyncReadExt, ReadHalf, WriteHalf};
use tokio::net::TcpStream;
use tokio::sync::{mpsc, oneshot, watch};

use tokio_rustls::TlsStream;
use tokio_util::sync::CancellationToken;
use tracing::trace;

static NEXT_SESSION_ID: AtomicU64 = AtomicU64::new(1);

// SessionId 类型定义
pub type SessionId = u64;

#[derive(Debug, Error)]
pub enum SessionError {
    #[error("Session is not in active state")]
    SessionNotActive,
    #[error("Stream not found: {stream_id}")]
    StreamNotFound { stream_id: u32 },
    #[error("Invalid stream state")]
    InvalidStreamState,
    #[error("Buffer overflow")]
    BufferOverflow,
    #[error("IO error: {0}")]
    IoError(#[from] std::io::Error),
    #[error("Session terminated")]
    SessionTerminated,
    #[error("Session cancelled")]
    SessionCancelled,
    #[error("Session shutdown")]
    SessionShutdown,
    #[error("Too many streams on session")]
    TooManyStreams,
    #[error("Failed to create session")]
    CreationFailed,
}

#[derive(Debug, Clone)]
pub struct StreamStats {
    pub bytes_sent: u64,
    pub bytes_received: u64,
    pub created_at: Instant,
    pub closed_at: Option<Instant>,
}

#[derive(Debug, PartialEq, Clone)]
pub enum SessionState {
    Initializing {
        start_time: Instant,
    },
    Authenticating {
        password_hash: Bytes,
        auth_sent_at: Option<Instant>,
    },
    SettingsNegotiation {
        settings_sent_at: Instant,
    },
    Active {
        established_at: Instant,
        active_streams: u32,
        heartbeat_interval: Option<Duration>,
        last_heartbeat_sent: Option<Instant>,
        last_heartbeat_received: Option<Instant>,
    },
    Draining {
        drain_started_at: Instant,
        remaining_streams: u32,
        force_close_at: Instant,
    },
    Terminated {
        terminated_at: Instant,
    },
}

// Actor消息定义
#[derive(Debug)]
pub enum SessionMessage {
    OpenStream {
        stream_id: u32,
        target_addr: SockAddr,
        response_tx: oneshot::Sender<Result<StreamHandle, SessionError>>,
    },
    CloseStream {
        stream_id: u32,
    },
    CloseStreamSend {
        stream_id: u32,
    },
    UpdatePadding {
        new_scheme: PaddingScheme,
    },
    StateTransition {
        new_state: SessionState,
    },
    FrameReceived {
        frame: Frame,
    },
    ConnectionError {
        error: std::io::Error,
    },
    HeartbeatTimer,
    Shutdown,
    SendData {
        stream_id: u32,
        data: Bytes,
    },
}

// 定义一个简单的状态枚举，用于 watch 通道
#[derive(Debug, Clone, PartialEq, Eq)]
pub enum SessionReadiness {
    Initializing,
    Active,
    Terminated,
}

// Session Actor的核心结构 - yamux模式
pub struct SessionActor {
    id: SessionId, // 使用 SessionId 类型
    state: SessionState,
    password_hash: Bytes,

    // 网络连接 - 直接包含reader和writer
    reader: ReadHalf<TlsStream<TcpStream>>,
    read_buffer: BytesMut,

    streams: HashMap<u32, StreamContext>,

    // 性能优化：缓存活跃流的ID以避免频繁的HashMap查找
    active_stream_ids: std::collections::HashSet<u32>,

    // 性能优化：帧合并发送队列
    pending_frames: smallvec::SmallVec<[Frame; 16]>,
    
    padding_writer: PaddingWriter<WriteHalf<TlsStream<TcpStream>>>,

    last_activity: Instant,
    message_tx: mpsc::Sender<SessionMessage>,
    crl_tx: mpsc::UnboundedSender<SessionMessage>,
    readiness_tx: watch::Sender<SessionReadiness>,

    // 超时管理器集成
    timeout_manager: TimeoutManagerHandle,
    // 当前活跃的超时Guards
    session_heartbeat_timeout: Option<TimeoutGuard>,
    session_state_timeout: Option<TimeoutGuard>,
    // 流超时映射 (stream_id -> timeout_guard)
    stream_timeouts: HashMap<u32, TimeoutGuard>,
}

// Session Handle - 外部接口
#[derive(Clone, Debug)]
pub struct SessionHandle {
    id: SessionId, // 添加 SessionId 字段
    crl_tx: mpsc::UnboundedSender<SessionMessage>,
    message_tx: mpsc::Sender<SessionMessage>,
    cancellation_token: CancellationToken,
    next_stream_id: Arc<AtomicU32>,
    readiness_rx: watch::Receiver<SessionReadiness>,
}

impl SessionHandle {
    /// 获取会话ID
    pub fn id(&self) -> SessionId {
        self.id
    }

    pub async fn open_stream(&self, target_addr: SockAddr) -> Result<StreamHandle, SessionError> {
        let stream_id = self.next_stream_id.fetch_add(1, Ordering::Relaxed);
        let (response_tx, response_rx) = oneshot::channel();

        self.crl_tx
            .send(SessionMessage::OpenStream {
                stream_id,
                target_addr,
                response_tx,
            })
            .map_err(|_| SessionError::SessionTerminated)?;

        response_rx
            .await
            .map_err(|_| SessionError::SessionTerminated)?
    }

    pub async fn close_stream(&self, stream_id: u32) -> Result<(), SessionError> {
        self.crl_tx
            .send(SessionMessage::CloseStream { stream_id })
            .map_err(|_| SessionError::SessionTerminated)?;
        Ok(())
    }

    pub async fn send_data(&self, stream_id: u32, data: Bytes) -> Result<(), SessionError> {
        self.message_tx
            .send(SessionMessage::SendData { stream_id, data })
            .await
            .map_err(|_| SessionError::SessionTerminated)?;
        Ok(())
    }

    pub async fn update_padding(&self, new_scheme: PaddingScheme) -> Result<(), SessionError> {
        self.crl_tx
            .send(SessionMessage::UpdatePadding { new_scheme })
            .map_err(|_| SessionError::SessionTerminated)?;
        Ok(())
    }

    pub async fn shutdown(&self) -> Result<(), SessionError> {
        let _ = self.crl_tx.send(SessionMessage::Shutdown);
        self.cancellation_token.cancel();
        Ok(())
    }

    pub fn is_cancelled(&self) -> bool {
        self.cancellation_token.is_cancelled()
    }

    pub fn cancellation_token(&self) -> CancellationToken {
        self.cancellation_token.clone()
    }

    pub async fn wait_for_active(&mut self) -> Result<(), SessionError> {
        // 如果当前已经是 Active，直接返回
        if *self.readiness_rx.borrow() == SessionReadiness::Active {
            return Ok(());
        }
        while self.readiness_rx.changed().await.is_ok() {
            let readiness = self.readiness_rx.borrow().clone();
            match readiness {
                SessionReadiness::Active => return Ok(()),
                SessionReadiness::Terminated => return Err(SessionError::SessionTerminated),
                SessionReadiness::Initializing => {
                    // 继续等待
                }
            }
        }
        // 如果通道被关闭，意味着 Actor 已退出
        Err(SessionError::SessionTerminated)
    }
}

impl SessionActor {
    // 使用结构化并发的启动方法
    pub async fn run_structured(
        stream: TlsStream<TcpStream>,
        password_hash: Bytes,
        timeout_manager: TimeoutManagerHandle,
        timeout_event_rx: mpsc::UnboundedReceiver<TimeoutEvent>,
    ) -> Result<SessionHandle, SessionError> {
        // 获取并增加原子计数器
        let session_id = NEXT_SESSION_ID.fetch_add(1, Ordering::Relaxed);

        let (reader, writer) = tokio::io::split(stream);
        let (crl_tx, crl_rx) = mpsc::unbounded_channel();
        let (message_tx, message_rx) = mpsc::channel(100); // 减少到 100 以降低内存占用
        let (readiness_tx, readiness_rx) = watch::channel(SessionReadiness::Initializing);
        let cancellation_token = CancellationToken::new();

        // 创建Actor
        let mut actor = Self {
            id: session_id, // 使用新的 session_id
            password_hash,
            state: SessionState::Initializing {
                start_time: Instant::now(),
            },
            reader,
            read_buffer: BytesMut::with_capacity(64 * 1024), // 64KB固定容量，无需池化
            streams: HashMap::new(),
            active_stream_ids: std::collections::HashSet::new(),
            pending_frames: smallvec::SmallVec::new(),
            padding_writer: PaddingWriter::new(PaddingScheme::default(), writer),
            last_activity: Instant::now(),
            message_tx: message_tx.clone(),
            crl_tx: crl_tx.clone(),
            readiness_tx,
            // 超时管理器
            timeout_manager: timeout_manager.clone(),
            session_heartbeat_timeout: None,
            session_state_timeout: None,
            stream_timeouts: HashMap::new(),
        };

        let handle = SessionHandle {
            id: session_id, // 添加 session_id
            crl_tx,
            message_tx,
            cancellation_token: cancellation_token.clone(),
            next_stream_id: Arc::new(AtomicU32::new(1)),
            readiness_rx,
        };

        // 注册初始超时
        actor.register_session_state_timeout();

        // 使用结构化并发启动主任务
        tokio::spawn(async move {
            tokio::select! {
                result = actor.run(message_rx, crl_rx, timeout_event_rx) => {
                    tracing::info!("Session {} Actor completed: {:?}", session_id, result);
                    result
                }

                // 等待取消信号
                _ = cancellation_token.cancelled() => {
                    tracing::info!("Session {} cancelled", session_id);
                    Ok(())
                }
            }
        });

        Ok(handle)
    }

    async fn run(
        &mut self,
        mut data_rx: mpsc::Receiver<SessionMessage>,
        mut ctl_rx: mpsc::UnboundedReceiver<SessionMessage>,
        mut timeout_event_rx: mpsc::UnboundedReceiver<TimeoutEvent>,
    ) -> Result<(), SessionError> {
        let mut heartbeat_timer = tokio::time::interval(Duration::from_secs(60));

        // 初始化状态处理
        if matches!(self.state, SessionState::Initializing { .. }) {
            if let Err(e) = self.handle_initialization().await {
                self.transition_to_closing(e).await;
                return Ok(());
            }
        }

        loop {
            tokio::select! {
                result = self.reader.read_buf(&mut self.read_buffer) => {
                    match result {
                        Ok(0) => {
                            tracing::info!("Session {} connection closed by remote", self.id);
                            self.transition_to_closing(SessionError::SessionTerminated).await;
                            break;
                        }
                        Ok(_n) => {
                            self.last_activity = Instant::now();

                            if let Err(e) = self.handle_network_data().await {
                                self.transition_to_closing(e).await;
                                break;
                            }
                        }
                        Err(e) => {
                            tracing::error!("Session {} network read error: {:?}", self.id, e);
                            self.transition_to_closing(SessionError::IoError(e)).await;
                            break;
                        }
                    }
                }

                // 超时事件处理
                timeout_event = timeout_event_rx.recv() => {
                    if let Some(event) = timeout_event {
                        // 只处理属于当前会话的超时事件
                        let should_handle = match &event {
                            TimeoutEvent::SessionActivity(id)
                            | TimeoutEvent::SessionHeartbeat(id)
                            | TimeoutEvent::SessionState(id) => *id == self.id,
                            TimeoutEvent::StreamFin(id, _) => *id == self.id,
                        };

                        if should_handle {
                            if let Err(e) = self.handle_timeout_event(event).await {
                                self.transition_to_closing(e).await;
                                break;
                            }
                        }
                    }
                }

                msg = ctl_rx.recv() => {
                    if let Some(msg) = msg {
                        if let Err(e) = self.handle_control_message(msg).await {
                            if matches!(e, SessionError::SessionShutdown) {
                                tracing::info!("Session {} shut down cleanly.", self.id);
                                return Ok(()); // 正常退出
                            }
                            self.transition_to_closing(e).await;
                            break;
                        }
                    } else {
                        tracing::info!("Session {} control channel closed", self.id);
                        break;
                    }
                }

                msg = data_rx.recv(), if matches!(self.state, SessionState::Active { .. }) => {
                    if let Some(msg) = msg {
                        if let Err(e) = self.handle_data_message(msg).await {
                            self.transition_to_closing(e).await;
                            break;
                        }
                    } else {
                        tracing::info!("Session {} data channel closed", self.id);
                        break;
                    }
                }

                // 心跳和活动超时检查
                _ = heartbeat_timer.tick() => {
                    // Check activity timeout (5 minutes) using timestamp comparison
                    // This is much more efficient than per-packet timer resets
                    if self.last_activity.elapsed() > Duration::from_secs(300) {
                        tracing::warn!("Session {} activity timeout", self.id);
                        self.transition_to_closing(SessionError::SessionTerminated).await;
                        break;
                    }

                    if let Err(e) = self.handle_heartbeat_tick().await {
                        self.transition_to_closing(e).await;
                        break;
                    }

                    // 心跳发送后启动心跳超时定时器（等待响应）
                    self.start_heartbeat_timeout();

                    // 只在非Active状态下重新注册状态超时
                    if !matches!(self.state, SessionState::Active { .. }) {
                        self.register_session_state_timeout();
                    }
                }
            }
        }

        self.cleanup().await?;
        Ok(())
    }

    fn register_session_state_timeout(&mut self) {
        if matches!(self.state, SessionState::Active { .. }) {
            return;
        }

        let timeout_duration = self.get_state_timeout_duration();

        // 如果超时时间过长或为0，也不注册
        if timeout_duration.as_secs() == 0 || timeout_duration.as_secs() > 3600 {
            // 超过1小时不注册
            return;
        }

        // 旧的 guard 会在被替换时自动 drop 并取消定时器
        self.session_state_timeout = Some(
            self.timeout_manager
                .register_timeout(timeout_duration, TimeoutEvent::SessionState(self.id)),
        );
    }

    async fn register_stream_fin_timeout(&mut self, stream_id: u32) {
        // 清除现有的流超时 - 旧的guard会自动drop并取消超时
        self.stream_timeouts.remove(&stream_id);

        // 注册新的FIN超时
        let guard = self.timeout_manager.register_timeout(
            Duration::from_secs(30), // 30秒FIN超时
            TimeoutEvent::StreamFin(self.id, stream_id),
        );

        self.stream_timeouts.insert(stream_id, guard);
        trace!("Registered FIN timeout for stream {}", stream_id);
    }

    fn start_heartbeat_timeout(&mut self) {
        // 在Active状态下才需要心跳超时管理
        if matches!(self.state, SessionState::Active { .. }) {
            // 启动心跳超时定时器，等待响应
            self.session_heartbeat_timeout = Some(self.timeout_manager.register_timeout(
                Duration::from_secs(90), // 90秒心跳超时
                TimeoutEvent::SessionHeartbeat(self.id),
            ));
        }
    }

    fn cancel_heartbeat_timeout(&mut self) {
        // 取消心跳超时定时器（收到响应时调用）
        self.session_heartbeat_timeout.take();
    }

    fn cancel_stream_timeout(&mut self, stream_id: u32) {
        // 从 map 中移除 guard，它的 Drop 方法会被调用，自动注销
        self.stream_timeouts.remove(&stream_id);
    }
    async fn handle_timeout_event(&mut self, event: TimeoutEvent) -> Result<(), SessionError> {
        match event {
            TimeoutEvent::SessionActivity(_) => {
                // Activity timeout is now handled via timestamp checking in heartbeat timer
                // This eliminates millions of unnecessary allocations from per-packet timer resets
                Ok(())
            }
            TimeoutEvent::SessionHeartbeat(session_id) if session_id == self.id => {
                tracing::warn!("Session {} heartbeat timeout", self.id);
                Err(SessionError::SessionTerminated)
            }
            TimeoutEvent::SessionState(session_id) if session_id == self.id => {
                tracing::warn!("Session {} state timeout", self.id);
                Err(SessionError::SessionTerminated)
            }
            TimeoutEvent::StreamFin(session_id, stream_id) if session_id == self.id => {
                if let Some(stream) = self.streams.get_mut(&stream_id) {
                    tracing::warn!("Stream {} FIN timeout, forcing close", stream_id);
                    stream.force_close();
                    self.cancel_stream_timeout(stream_id); // 取消超时管理器中的超时
                    self.active_stream_ids.remove(&stream_id);
                }
                Ok(())
            }
            _ => Ok(()), // 忽略不相关的超时事件
        }
    }

    fn get_state_timeout_duration(&self) -> Duration {
        match &self.state {
            SessionState::Authenticating { auth_sent_at, .. } => {
                if let Some(sent_at) = auth_sent_at {
                    Duration::from_secs(10).saturating_sub(sent_at.elapsed())
                } else {
                    Duration::from_secs(10)
                }
            }
            SessionState::SettingsNegotiation { settings_sent_at } => {
                Duration::from_secs(15).saturating_sub(settings_sent_at.elapsed())
            }
            SessionState::Initializing { .. } => {
                Duration::from_secs(30) // 初始化超时
            }
            SessionState::Active { .. } => Duration::from_secs(0),
            SessionState::Draining { force_close_at, .. } => {
                force_close_at.saturating_duration_since(Instant::now())
            }
            SessionState::Terminated { .. } => {
                Duration::from_secs(0) // 立即超时
            }
        }
    }

    async fn transition_to_closing(&mut self, error: SessionError) {
        tracing::warn!("Session {} transitioning to closing: {:?}", self.id, error);

        for (stream_id, stream) in &mut self.streams {
            stream.set_state(StreamState::Closed);
            tracing::debug!("Closing stream {} due to session error", stream_id);
        }

        self.transition_to(SessionState::Terminated {
            terminated_at: Instant::now(),
        });
    }

    async fn handle_network_data(&mut self) -> Result<(), SessionError> {
        // 批量处理多个帧以提高效率，使用读取-解析-消费模式
        let mut frame_count = 0;
        const MAX_FRAMES_PER_BATCH: usize = 16; // 限制单次处理的帧数以避免饿死其他任务

        while frame_count < MAX_FRAMES_PER_BATCH {
            // 使用池化帧解析函数：避免动态分配，使用固定大小内存池
            match Frame::try_parse_pooled(&self.read_buffer)? {
                Some((pooled_frame, consumed_bytes)) => {
                    // 处理池化帧
                    self.handle_pooled_frame(pooled_frame).await?;

                    // 消费已处理的字节数
                    self.read_buffer.advance(consumed_bytes);
                    frame_count += 1;
                }
                None => break, // 没有完整的帧，等待更多数据
            }
        }

        // 缓冲区清理：当处理完批量帧后，如果缓冲区过大则重置为标准大小
        if self.read_buffer.capacity() > 256 * 1024 && self.read_buffer.len() < 8192 {
            let remaining_data = self.read_buffer.split().freeze();
            self.read_buffer = BytesMut::with_capacity(64 * 1024);
            if !remaining_data.is_empty() {
                self.read_buffer.extend_from_slice(&remaining_data);
            }
        }

        Ok(())
    }

    async fn handle_control_message(
        &mut self,
        message: SessionMessage,
    ) -> Result<(), SessionError> {
        self.last_activity = Instant::now();

        match message {
            SessionMessage::OpenStream {
                stream_id,
                target_addr,
                response_tx,
            } => {
                let result = self.handle_open_stream(stream_id, target_addr).await;
                let _ = response_tx.send(result);
            }
            SessionMessage::CloseStream { stream_id } => {
                self.handle_close_stream(stream_id).await?;
            }
            SessionMessage::CloseStreamSend { stream_id } => {
                self.handle_close_stream_send(stream_id).await?;
            }
            SessionMessage::UpdatePadding { new_scheme } => {
                self.padding_writer.update_padding_scheme(new_scheme);
            }
            SessionMessage::StateTransition { new_state } => {
                self.transition_to(new_state);
            }
            SessionMessage::FrameReceived { frame } => {
                self.handle_frame(frame).await?;
            }
            SessionMessage::ConnectionError { error } => {
                tracing::error!("Connection error: {:?}", error);
                return Err(SessionError::IoError(error));
            }
            SessionMessage::HeartbeatTimer => {
                self.handle_heartbeat().await?;
            }
            SessionMessage::Shutdown => {
                tracing::info!("Session {} shutdown requested", self.id);
                return Err(SessionError::SessionShutdown);
            }
            SessionMessage::SendData { .. } => {
                tracing::warn!("Received data message in control channel");
            }
        }

        Ok(())
    }

    async fn handle_data_message(&mut self, message: SessionMessage) -> Result<(), SessionError> {
        self.last_activity = Instant::now();

        match message {
            SessionMessage::SendData { stream_id, data } => {
                self.handle_send_data(stream_id, data).await?;
            }
            _ => {
                tracing::warn!("Received non-data message in data channel: {:?}", message);
            }
        }

        Ok(())
    }

    async fn handle_heartbeat_tick(&mut self) -> Result<(), SessionError> {
        if matches!(self.state, SessionState::Active { .. }) {
            self.handle_heartbeat().await?;
        }
        Ok(())
    }

    async fn handle_open_stream(
        &mut self,
        stream_id: u32,
        target_addr: SockAddr,
    ) -> Result<StreamHandle, SessionError> {
        // Simple, clean guard clause.
        if !matches!(self.state, SessionState::Active { .. }) {
            // We will improve this in Step 5 to queue requests. For now, just reject.
            tracing::warn!(
                "Session {} received open_stream request while not active. State: {:?}",
                self.id,
                self.state
            );
            return Err(SessionError::SessionNotActive);
        }

        if self.streams.contains_key(&stream_id) {
            return Err(SessionError::InvalidStreamState);
        }

        let syn_frame = Frame {
            command: Command::Syn,
            stream_id,
            data: Bytes::new(),
        };
        self.queue_frame(syn_frame);

        let target_frame = Frame {
            command: Command::Psh,
            stream_id,
            data: target_addr.to_bytes(),
        };

        tracing::debug!(
            "Session {} sending target address for stream {}: {}",
            self.id,
            stream_id,
            format!("{:?}", target_addr),
        );

        self.queue_frame(target_frame);
        self.flush_pending_frames().await?;

        let (mut stream_context, data_rx, state_rx) =
            StreamContext::new(stream_id, target_addr.clone());
        stream_context.set_state(StreamState::Established);
        self.streams.insert(stream_id, stream_context);
        self.active_stream_ids.insert(stream_id);

        let stream_handle = StreamHandle::new(
            stream_id,
            self.message_tx.clone(),
            self.crl_tx.clone(),
            data_rx,
            state_rx,
        );

        tracing::debug!("Stream {} opened successfully", stream_id);
        Ok(stream_handle)
    }

    async fn handle_close_stream(&mut self, stream_id: u32) -> Result<(), SessionError> {
        let should_send_fin = if let Some(stream) = self.streams.get(&stream_id) {
            let current_state = stream.get_state();

            match current_state {
                StreamState::Established | StreamState::RemoteClosed => true,
                StreamState::LocalClosed | StreamState::Closed => {
                    tracing::debug!("Stream {} already closing/closed", stream_id);
                    false
                }
            }
        } else {
            false
        };

        if should_send_fin {
            // 先发送 FIN
            self.send_fin_frame(stream_id).await?;

            // 然后更新状态
            if let Some(stream) = self.streams.get_mut(&stream_id) {
                let current_state = stream.get_state();

                match current_state {
                    StreamState::Established => {
                        stream.set_state(StreamState::LocalClosed);
                        // 使用 timeout_manager 管理 FIN 超时，不再使用 stream 内部的超时机制
                        tracing::debug!("Stream {} local closed, FIN sent", stream_id);
                    }
                    StreamState::RemoteClosed => {
                        stream.set_state(StreamState::Closed);
                        // 取消超时管理器中的超时
                        self.cancel_stream_timeout(stream_id);
                        self.active_stream_ids.remove(&stream_id);
                        tracing::debug!("Stream {} fully closed", stream_id);
                    }
                    _ => {}
                }
            }
        }

        Ok(())
    }

    async fn handle_close_stream_send(&mut self, stream_id: u32) -> Result<(), SessionError> {
        // 这个方法只关闭发送端，等价于 handle_close_stream
        self.handle_close_stream(stream_id).await
    }

    async fn send_fin_frame(&mut self, stream_id: u32) -> Result<(), SessionError> {
        let fin_frame = Frame {
            command: Command::Fin,
            stream_id,
            data: Bytes::new(),
        };
        self.queue_frame(fin_frame);

        // 注册FIN超时
        self.register_stream_fin_timeout(stream_id).await;

        // FIN帧需要立即发送
        self.flush_pending_frames().await
    }

    async fn handle_send_data(&mut self, stream_id: u32, data: Bytes) -> Result<(), SessionError> {
        match self.streams.get(&stream_id) {
            Some(stream) => {
                let state = stream.get_state();
                if state == StreamState::Established || state == StreamState::RemoteClosed {
                    let psh_frame = Frame {
                        command: Command::Psh,
                        stream_id,
                        data,
                    };
                    self.send_frame(psh_frame).await
                } else {
                    Err(SessionError::InvalidStreamState)
                }
            }
            None => Err(SessionError::StreamNotFound { stream_id }),
        }
    }

    async fn handle_frame(&mut self, frame: Frame) -> Result<(), SessionError> {
        match frame.command {
            Command::SynAck => {
                if let Some(stream) = self.streams.get_mut(&frame.stream_id) {
                    if frame.data.is_empty() {
                        tracing::debug!("Stream {} health check passed", frame.stream_id);
                    } else {
                        stream.set_state(StreamState::Closed);
                        tracing::warn!(
                            "Stream {} health check failed: {:?}",
                            frame.stream_id,
                            frame.data
                        );
                    }
                }
            }
            Command::Psh => {
                // 强制记录所有 PSH 数据帧，包括空数据
                tracing::info!(
                    "Session {} received PSH data for stream {}: {} bytes",
                    self.id,
                    frame.stream_id,
                    frame.data.len()
                );

                if let Some(stream) = self.streams.get_mut(&frame.stream_id) {
                    if let Err(e) = stream.send_data(frame.data.clone()).await {
                        tracing::error!(
                            "Failed to send PSH data to stream {}: {:?}",
                            frame.stream_id,
                            e
                        );
                    } else {
                        tracing::debug!(
                            "Successfully sent {} bytes to stream {}",
                            frame.data.len(),
                            frame.stream_id
                        );
                    }
                } else {
                    tracing::warn!(
                        "Received PSH data for unknown stream {}: {} bytes",
                        frame.stream_id,
                        frame.data.len()
                    );
                }
            }
            Command::Fin => {
                if let Some(stream) = self.streams.get_mut(&frame.stream_id) {
                    let current_state = stream.get_state();

                    match current_state {
                        StreamState::Established => {
                            // 远程关闭，但本地还可以发送数据
                            stream.set_state(StreamState::RemoteClosed);
                            tracing::debug!("Stream {} remote closed", frame.stream_id);
                        }
                        StreamState::LocalClosed => {
                            // 本地已关闭，远程也关闭，完全关闭连接
                            stream.set_state(StreamState::Closed);
                            stream.cancel_fin_timeout(); // 取消超时任务
                            self.cancel_stream_timeout(frame.stream_id); // 取消超时管理器中的超时
                            self.active_stream_ids.remove(&frame.stream_id);
                            tracing::debug!("Stream {} fully closed", frame.stream_id);
                        }
                        StreamState::RemoteClosed | StreamState::Closed => {
                            // 已经是远程关闭或已关闭状态，不需要处理
                        }
                    }
                } else {
                    tracing::warn!("Received FIN for unknown stream {}", frame.stream_id);
                }
            }
            Command::HeartRequest => {
                let response = Frame {
                    command: Command::HeartResponse,
                    stream_id: 0,
                    data: Bytes::new(),
                };
                self.send_frame(response).await?;
            }
            Command::ServerSettings => {
                tracing::trace!(
                    "Session {} received ServerSettings: {}",
                    self.id,
                    String::from_utf8_lossy(&frame.data)
                );

                if matches!(self.state, SessionState::SettingsNegotiation { .. }) {
                    tracing::trace!(
                        "Session {} handshake complete. Session is now active.",
                        self.id
                    );
                    self.transition_to(SessionState::Active {
                        established_at: Instant::now(),
                        active_streams: 0,
                        heartbeat_interval: Some(Duration::from_secs(60)),
                        last_heartbeat_sent: Some(Instant::now()),
                        last_heartbeat_received: Some(Instant::now()),
                    });
                } else {
                    tracing::warn!(
                        "Received ServerSettings frame in unexpected state: {:?}",
                        self.state
                    );
                }
            }
            Command::Waste => {
                // 打印 Waste 帧
                tracing::trace!(
                    "Session {} waste {}",
                    self.id,
                    String::from_utf8_lossy(&frame.data)
                );
            }
            Command::Syn => {
                // 打印 Syn 帧
                tracing::trace!(
                    "Session {} syn {}",
                    self.id,
                    String::from_utf8_lossy(&frame.data)
                );
            }
            Command::Settings => {
                // 打印 Settings 帧
                tracing::trace!(
                    "Session {} settings {}",
                    self.id,
                    String::from_utf8_lossy(&frame.data)
                );
            }
            Command::Alert => {
                // 打印 Alert 帧
                tracing::error!(
                    "Session {} alert {}",
                    self.id,
                    String::from_utf8_lossy(&frame.data)
                );
                self.transition_to(SessionState::Terminated {
                    terminated_at: Instant::now(),
                });
            }
            Command::UpdatePaddingScheme => {
                let padding_scheme = PaddingScheme::parse(&frame.data).unwrap();
                self.padding_writer.update_padding_scheme(padding_scheme);
            }
            Command::HeartResponse => {
                // 收到 HeartResponse 帧，更新最后收到心跳的时间
                if let SessionState::Active {
                    ref mut last_heartbeat_received,
                    ..
                } = self.state
                {
                    *last_heartbeat_received = Some(Instant::now());
                    tracing::trace!("Session {} received heartbeat response", self.id);
                }

                // 收到心跳响应，取消心跳超时定时器
                self.cancel_heartbeat_timeout();
                // 更新活动时间戳
                self.last_activity = Instant::now();
            }
        }

        Ok(())
    }

    /// 处理池化帧的方法 - 新的"甜点区"方案的核心
    /// 使用固定大小内存池避免动态分配，减少40GB的Churn
    async fn handle_pooled_frame(&mut self, pooled_frame: PooledFrame) -> Result<(), SessionError> {
        // 仅在debug模式下记录详细日志
        #[cfg(debug_assertions)]
        tracing::debug!(
            "Session {} received pooled frame: command={:?}, stream_id={}, data_len={}",
            self.id,
            pooled_frame.command,
            pooled_frame.stream_id,
            pooled_frame.data_len()
        );

        match pooled_frame.command {
            Command::SynAck => {
                if let Some(stream) = self.streams.get_mut(&pooled_frame.stream_id) {
                    if pooled_frame.data.is_empty() {
                        tracing::debug!("Stream {} health check passed", pooled_frame.stream_id);
                    } else {
                        stream.set_state(StreamState::Closed);
                        tracing::warn!(
                            "Stream {} health check failed: {:?}",
                            pooled_frame.stream_id,
                            String::from_utf8_lossy(pooled_frame.data.as_slice())
                        );
                    }
                }
            }
            Command::Psh => {
                // 强制记录所有 PSH 数据帧，包括空数据
                tracing::info!(
                    "Session {} received PSH data for stream {}: {} bytes",
                    self.id,
                    pooled_frame.stream_id,
                    pooled_frame.data_len()
                );

                if let Some(stream) = self.streams.get_mut(&pooled_frame.stream_id) {
                    // 在消费 pooled_frame.data 之前获取长度
                    let data_len = pooled_frame.data_len();
                    // 将池化缓冲区转换为 Bytes，这里会进行最终的内存拷贝
                    // 但是我们使用的是固定大小的池化内存，而不是动态分配
                    let data_bytes = pooled_frame.data.into_bytes();
                    if let Err(e) = stream.send_data(data_bytes).await {
                        tracing::error!(
                            "Failed to send PSH data to stream {}: {:?}",
                            pooled_frame.stream_id,
                            e
                        );
                    } else {
                        tracing::debug!(
                            "Successfully sent {} bytes to stream {}",
                            data_len,
                            pooled_frame.stream_id
                        );
                    }
                } else {
                    tracing::warn!(
                        "Received PSH data for unknown stream {}: {} bytes",
                        pooled_frame.stream_id,
                        pooled_frame.data_len()
                    );
                }
            }
            Command::Fin => {
                if let Some(stream) = self.streams.get_mut(&pooled_frame.stream_id) {
                    let current_state = stream.get_state();

                    match current_state {
                        StreamState::Established => {
                            // 远程关闭，但本地还可以发送数据
                            stream.set_state(StreamState::RemoteClosed);
                            tracing::debug!("Stream {} remote closed", pooled_frame.stream_id);
                        }
                        StreamState::LocalClosed => {
                            // 本地已关闭，远程也关闭，完全关闭连接
                            stream.set_state(StreamState::Closed);
                            stream.cancel_fin_timeout(); // 取消超时任务
                            self.cancel_stream_timeout(pooled_frame.stream_id); // 取消超时管理器中的超时
                            self.active_stream_ids.remove(&pooled_frame.stream_id);
                            tracing::debug!("Stream {} fully closed", pooled_frame.stream_id);
                        }
                        StreamState::RemoteClosed | StreamState::Closed => {
                            // 已经是远程关闭或已关闭状态，不需要处理
                        }
                    }
                } else {
                    tracing::warn!("Received FIN for unknown stream {}", pooled_frame.stream_id);
                }
            }
            Command::HeartRequest => {
                let response = Frame {
                    command: Command::HeartResponse,
                    stream_id: 0,
                    data: Bytes::new(),
                };
                self.send_frame(response).await?;
            }
            Command::ServerSettings => {
                tracing::trace!(
                    "Session {} received ServerSettings: {}",
                    self.id,
                    String::from_utf8_lossy(pooled_frame.data.as_slice())
                );

                if matches!(self.state, SessionState::SettingsNegotiation { .. }) {
                    tracing::trace!(
                        "Session {} handshake complete. Session is now active.",
                        self.id
                    );
                    self.transition_to(SessionState::Active {
                        established_at: Instant::now(),
                        active_streams: 0,
                        heartbeat_interval: Some(Duration::from_secs(60)),
                        last_heartbeat_sent: Some(Instant::now()),
                        last_heartbeat_received: Some(Instant::now()),
                    });
                } else {
                    tracing::warn!(
                        "Received ServerSettings frame in unexpected state: {:?}",
                        self.state
                    );
                }
            }
            Command::Waste => {
                // 打印 Waste 帧
                tracing::trace!(
                    "Session {} waste {}",
                    self.id,
                    String::from_utf8_lossy(pooled_frame.data.as_slice())
                );
            }
            Command::Syn => {
                // 打印 Syn 帧
                tracing::trace!(
                    "Session {} syn {}",
                    self.id,
                    String::from_utf8_lossy(pooled_frame.data.as_slice())
                );
            }
            Command::Settings => {
                // 打印 Settings 帧
                tracing::trace!(
                    "Session {} settings {}",
                    self.id,
                    String::from_utf8_lossy(pooled_frame.data.as_slice())
                );
            }
            Command::Alert => {
                // 打印 Alert 帧
                tracing::error!(
                    "Session {} alert {}",
                    self.id,
                    String::from_utf8_lossy(pooled_frame.data.as_slice())
                );
                self.transition_to(SessionState::Terminated {
                    terminated_at: Instant::now(),
                });
            }
            Command::UpdatePaddingScheme => {
                let padding_scheme = PaddingScheme::parse(pooled_frame.data.as_slice()).unwrap();
                self.padding_writer.update_padding_scheme(padding_scheme);
            }
            Command::HeartResponse => {
                // 收到 HeartResponse 帧，更新最后收到心跳的时间
                if let SessionState::Active {
                    ref mut last_heartbeat_received,
                    ..
                } = self.state
                {
                    *last_heartbeat_received = Some(Instant::now());
                    tracing::trace!("Session {} received heartbeat response", self.id);
                }

                // 收到心跳响应，取消心跳超时定时器
                self.cancel_heartbeat_timeout();
                // 更新活动时间戳
                self.last_activity = Instant::now();
            }
        }

        Ok(())
    }

    async fn handle_heartbeat(&mut self) -> Result<(), SessionError> {
        if matches!(self.state, SessionState::Active { .. }) {
            let heartbeat_frame = Frame {
                command: Command::HeartRequest,
                stream_id: 0,
                data: Bytes::new(),
            };

            self.send_frame(heartbeat_frame).await?;
        }

        Ok(())
    }

    async fn handle_initialization(&mut self) -> Result<(), SessionError> {
        // --- Part 1: Send Authentication Frame ---
        let padding_data = self.padding_writer.get_padding_data(0);
        let mut auth_data_buf = BytesMut::new();

        // 预分配容量避免重新分配
        let total_auth_size = self.password_hash.len() + padding_data.len();
        auth_data_buf.reserve(total_auth_size);

        auth_data_buf.extend_from_slice(&self.password_hash);
        auth_data_buf.extend_from_slice(&padding_data);

        let auth_data_bytes = auth_data_buf.freeze();

        self.padding_writer.write(auth_data_bytes).await?;
        tracing::debug!("Session {} authentication data sent", self.id);

        let padding_md5 = self
            .padding_writer
            .get_padding_scheme()
            .get_md5()
            .to_string();

        let mut settings_buf = BytesMut::new();

        let client_name = env!("CARGO_PKG_NAME").as_bytes();
        let client_version = env!("CARGO_PKG_VERSION").as_bytes();
        let padding_md5_bytes = padding_md5.as_bytes();

        // 预分配容量
        let estimated_capacity = b"v=2\nclient=".len() +
                               client_name.len() +
                               1 + // "/"
                               client_version.len() +
                               b"\npadding-md5=".len() +
                               padding_md5_bytes.len();
        settings_buf.reserve(estimated_capacity);

        // 直接在buffer中构建，避免中间字符串分配
        settings_buf.extend_from_slice(b"v=2\nclient=");
        settings_buf.extend_from_slice(client_name);
        settings_buf.extend_from_slice(b"/");
        settings_buf.extend_from_slice(client_version);
        settings_buf.extend_from_slice(b"\npadding-md5=");
        settings_buf.extend_from_slice(padding_md5_bytes);

        let settings_data = settings_buf.freeze();

        let settings_frame = Frame {
            command: Command::Settings,
            stream_id: 0,
            data: settings_data,
        };
        self.queue_frame(settings_frame);
        // 设置帧需要立即发送
        self.flush_pending_frames().await?;
        tracing::debug!("Session {} initial settings frame sent", self.id);

        self.transition_to(SessionState::SettingsNegotiation {
            settings_sent_at: Instant::now(),
        });
        tracing::info!(
            "Session {} handshaking, awaiting server settings...",
            self.id
        );

        Ok(())
    }

    /// 将帧加入发送队列（用于批量发送优化）
    #[inline]
    fn queue_frame(&mut self, frame: Frame) {
        self.pending_frames.push(frame);
    }

    /// 批量发送所有待发送的帧
    async fn flush_pending_frames(&mut self) -> Result<(), SessionError> {
        if self.pending_frames.is_empty() {
            return Ok(());
        }

        let estimated_size: usize = self
            .pending_frames
            .iter()
            .map(|f| f.data.len() + FRAME_HEADER_SIZE)
            .sum();
        
        let mut batch_buffer = crate::utils::GLOBAL_BUFFER_POOL.get_reusable_bytesmut(estimated_size);

        let frame_count = self.pending_frames.len();
        for frame in self.pending_frames.drain(..) {
            frame.encode_to_buffer(batch_buffer.as_mut_bytesmut()); // 使用新的方法获取 BytesMut
        }

        let batch_data = batch_buffer.freeze_zero_copy(); 

        self.padding_writer.write(batch_data).await?;

        tracing::trace!(
            "Session {} flushed {} frames in batch",
            self.id,
            frame_count
        );
        Ok(())
    }

    async fn send_frame(&mut self, frame: Frame) -> Result<(), SessionError> {
        self.queue_frame(frame);
        self.flush_pending_frames().await?;
        Ok(())
    }

    fn transition_to(&mut self, new_state: SessionState) {
        let old_state = std::mem::replace(&mut self.state, new_state);

        tracing::info!(
            session_id = %self.id,
            old_state = ?old_state,
            new_state = ?self.state,
            "Session state transition"
        );

        let new_readiness = match self.state {
            SessionState::Active { .. } => SessionReadiness::Active,
            SessionState::Terminated { .. } => SessionReadiness::Terminated,
            _ => SessionReadiness::Initializing, // Covers Initializing and SettingsNegotiation
        };

        let _ = self.readiness_tx.send(new_readiness);

        self.session_state_timeout.take();
    }

    async fn cleanup(&mut self) -> Result<(), SessionError> {
        // TimeoutGuard 的 RAII 特性会自动处理所有超时的清理
        // 当这些 Option<TimeoutGuard> 和 HashMap<u32, TimeoutGuard> 被 drop 时，
        // 所有的 guard 都会自动发送取消消息到 TimeoutManager
        tracing::info!(
            "Session {} cleanup - RAII guards will handle timeout cleanup",
            self.id
        );
        self.stream_timeouts.clear();

        // 收集所有流ID
        let stream_ids: Vec<u32> = self.streams.keys().copied().collect();

        // 关闭所有流并取消超时任务
        for stream_id in stream_ids {
            if let Some(_stream) = self.streams.remove(&stream_id) {
                // 所有超时管理已转移到 timeout_manager，不再需要调用 stream.cancel_fin_timeout()

                let fin_frame = Frame {
                    command: Command::Fin,
                    stream_id,
                    data: Bytes::new(),
                };
                self.send_frame(fin_frame).await?;
            }
        }

        tracing::info!("Session {} cleaned up", self.id);
        Ok(())
    }
}
