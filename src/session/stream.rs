use crate::SockAddr;
use crate::session::inner::{SessionError, SessionMessage};
use bytes::Bytes;
use std::pin::Pin;
use std::task::{Context, Poll};
use std::future::Future;

use tokio::io::{AsyncRead, AsyncWrite, ReadBuf};
use tokio::sync::{mpsc, watch};



#[derive(Debug, Clone, PartialEq)]
pub struct StreamInfo {
    pub id: u32,
    target_addr: SockAddr,
    state: StreamState,
    pending_data: smallvec::SmallVec<[Bytes; 16]>,
}

impl StreamInfo {
    pub fn new(id: u32, target_addr: SockAddr) -> Self {
        Self {
            id,
            target_addr,
            state: StreamState::Established,
            pending_data: smallvec::SmallVec::new(),
        }
    }

    // 修改stream状态
    pub fn set_state(&mut self, state: StreamState) {
        self.state = state;
    }

    pub fn get_state(&self) -> StreamState {
        self.state
    }

    pub fn get_pending_data(&mut self) -> smallvec::SmallVec<[Bytes; 16]> {
        std::mem::take(&mut self.pending_data)
    }

    pub fn add_pending_data(&mut self, data: Bytes) {
        self.pending_data.push(data);
    }

    pub fn clear_pending_data(&mut self) {
        self.pending_data.clear();
    }
}

#[derive(Debug, PartialEq, Clone, Copy)] // 添加Copy trait
pub enum StreamState {
    Established,  // 双向都可以发送数据
    LocalClosed,  // 本地已发送 FIN，不再发送数据，但仍可接收数据
    RemoteClosed, // 远程已发送 FIN，不再接收数据，但仍可发送数据
    Closed,       // 双向都已关闭
}

pub struct StreamHandle {
    stream_id: u32,

    // 发送消息到Session Actor的通道
    message_tx: mpsc::Sender<SessionMessage>,

    // 发送消息到Session Actor的通道
    crl_tx: mpsc::UnboundedSender<SessionMessage>,

    // 接收数据的通道
    data_rx: mpsc::Receiver<Bytes>,
    read_buffer: Vec<u8>,

    // 流状态监听
    state_rx: watch::Receiver<StreamState>,

    // 正在进行的写操作 Future
    pending_write: Option<
        Pin<Box<dyn Future<Output = Result<(), mpsc::error::SendError<SessionMessage>>> + Send>>,
    >,

    // 流是否已关闭
    is_closed: bool,
}

impl std::fmt::Debug for StreamHandle {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        f.debug_struct("StreamHandle")
            .field("stream_id", &self.stream_id)
            .field("is_closed", &self.is_closed)
            .finish()
    }
}

impl StreamHandle {
    pub fn new(
        stream_id: u32,
        message_tx: mpsc::Sender<SessionMessage>,
        crl_tx: mpsc::UnboundedSender<SessionMessage>,
        data_rx: mpsc::Receiver<Bytes>,
        state_rx: watch::Receiver<StreamState>,
    ) -> Self {
        Self {
            stream_id,
            message_tx,
            crl_tx,
            data_rx,
            read_buffer: Vec::new(),
            state_rx,
            pending_write: None,
            is_closed: false,
        }
    }

    pub fn stream_id(&self) -> u32 {
        self.stream_id
    }

    pub fn state(&self) -> StreamState {
        *self.state_rx.borrow() // 现在可以解引用，因为StreamState实现了Copy
    }
}

impl AsyncRead for StreamHandle {
    fn poll_read(
        mut self: Pin<&mut Self>,
        cx: &mut Context<'_>,
        buf: &mut ReadBuf<'_>,
    ) -> Poll<std::io::Result<()>> {
        // 首先尝试从缓冲区读取
        if !self.read_buffer.is_empty() {
            let to_read = std::cmp::min(buf.remaining(), self.read_buffer.len());
            buf.put_slice(&self.read_buffer[..to_read]);
            self.read_buffer.drain(..to_read);
            return Poll::Ready(Ok(()));
        }

        // 检查流是否已关闭
        if self.is_closed || *self.state_rx.borrow() == StreamState::Closed {
            return Poll::Ready(Ok(())); // EOF
        }

        // 尝试接收新数据
        match self.data_rx.poll_recv(cx) {
            Poll::Ready(Some(data)) => {
                let to_read = std::cmp::min(buf.remaining(), data.len());
                buf.put_slice(&data[..to_read]);

                // 如果还有剩余数据，存入缓冲区
                if to_read < data.len() {
                    self.read_buffer.extend_from_slice(&data[to_read..]);
                }

                Poll::Ready(Ok(()))
            }
            Poll::Ready(None) => {
                // 通道关闭，表示EOF
                self.is_closed = true;
                Poll::Ready(Ok(()))
            }
            Poll::Pending => Poll::Pending,
        }
    }
}

impl AsyncWrite for StreamHandle {
    fn poll_write(
        mut self: Pin<&mut Self>,
        cx: &mut Context<'_>,
        buf: &[u8],
    ) -> Poll<Result<usize, std::io::Error>> {
        let current_state = *self.state_rx.borrow();

        // 检查流是否已关闭或本地已关闭写入
        if self.is_closed
            || current_state == StreamState::Closed
            || current_state == StreamState::LocalClosed
        {
            return Poll::Ready(Err(std::io::Error::new(
                std::io::ErrorKind::BrokenPipe,
                "Stream write is closed",
            )));
        }

        // 1. 如果有正在进行的写操作，先处理它
        if let Some(mut future) = self.pending_write.take() {
            match future.as_mut().poll(cx) {
                Poll::Ready(Ok(())) => {
                    // 上一个写操作完成了，可以继续处理本次的 buf
                }
                Poll::Ready(Err(_)) => {
                    // 通道关闭
                    return Poll::Ready(Err(std::io::Error::new(
                        std::io::ErrorKind::BrokenPipe,
                        "Session terminated",
                    )));
                }
                Poll::Pending => {
                    // 上一个写操作还没完成，把 future 放回去，然后返回 Pending
                    self.pending_write = Some(future);
                    return Poll::Pending;
                }
            }
        }

        // 2. 如果 buf 为空，直接返回成功
        if buf.is_empty() {
            return Poll::Ready(Ok(0));
        }
        
        let mut batch_buffer = crate::utils::GLOBAL_BUFFER_POOL.get_reusable_bytesmut(buf.len());
        batch_buffer.extend_from_slice(buf);
        let data = batch_buffer.freeze_zero_copy();

        let msg = SessionMessage::SendData {
            stream_id: self.stream_id,
            data,
        };

        let sender = self.message_tx.clone();

        // 4. 重用 Future，避免 Box::pin 分配
        let mut write_future = Box::pin(async move { sender.send(msg).await });

        // 5. 立即 poll 一次这个新的 Future
        match write_future.as_mut().poll(cx) {
            Poll::Ready(Ok(())) => {
                // 通道有空间，一次就成功了！
                Poll::Ready(Ok(buf.len()))
            }
            Poll::Ready(Err(_)) => {
                // 通道关闭
                Poll::Ready(Err(std::io::Error::new(
                    std::io::ErrorKind::BrokenPipe,
                    "Session terminated",
                )))
            }
            Poll::Pending => {
                // 通道满了，我们将这个 Future 存起来，下次 poll_write 时继续
                self.pending_write = Some(write_future);
                Poll::Pending
            }
        }
    }

    fn poll_flush(
        mut self: Pin<&mut Self>,
        cx: &mut Context<'_>,
    ) -> Poll<Result<(), std::io::Error>> {
        if let Some(mut future) = self.pending_write.take() {
            match future.as_mut().poll(cx) {
                Poll::Ready(Ok(())) => Poll::Ready(Ok(())),
                Poll::Ready(Err(_)) => Poll::Ready(Err(std::io::Error::new(
                    std::io::ErrorKind::BrokenPipe,
                    "Session terminated",
                ))),
                Poll::Pending => {
                    // 还没完成，放回去，返回 Pending
                    self.pending_write = Some(future);
                    Poll::Pending
                }
            }
        } else {
            // 没有正在进行的写操作，说明已经 flush 完了
            Poll::Ready(Ok(()))
        }
    }

    fn poll_shutdown(
        mut self: Pin<&mut Self>,
        _cx: &mut Context<'_>,
    ) -> Poll<Result<(), std::io::Error>> {
        if !self.is_closed {
            match self.crl_tx.send(SessionMessage::CloseStream {
                stream_id: self.stream_id,
            }) {
                Ok(_) => {
                    self.is_closed = true;
                    Poll::Ready(Ok(()))
                }
                Err(mpsc::error::SendError(_)) => Poll::Ready(Err(std::io::Error::new(
                    std::io::ErrorKind::BrokenPipe,
                    "Session terminated",
                ))),
            }
        } else {
            Poll::Ready(Ok(()))
        }
    }
}

// 扩展StreamInfo以支持数据接收
pub struct StreamContext {
    pub info: StreamInfo,
    pub data_tx: mpsc::Sender<Bytes>,         // 发送接收到的数据
    pub state_tx: watch::Sender<StreamState>, // 发送状态变化
}

impl StreamContext {
    pub fn new(
        stream_id: u32,
        target_addr: SockAddr,
    ) -> (Self, mpsc::Receiver<Bytes>, watch::Receiver<StreamState>) {
        let (data_tx, data_rx) = mpsc::channel(100); // 每个流的数据缓冲
        let (state_tx, state_rx) = watch::channel(StreamState::Established); // 使用已有的状态

        let context = Self {
            info: StreamInfo::new(stream_id, target_addr),
            data_tx,
            state_tx,
        };

        (context, data_rx, state_rx)
    }

    pub fn set_state(&mut self, state: StreamState) {
        self.info.set_state(state);
        let _ = self.state_tx.send(state); // 通知StreamHandle状态变化
    }

    pub fn get_state(&self) -> StreamState {
        self.info.get_state()
    }

    /// 启动 FIN 超时（设置超时时间点）
    /// 已迁移到 timeout_manager，此方法保留为空实现以保持兼容性
    pub fn start_fin_timeout(&mut self, _crl_tx: mpsc::UnboundedSender<SessionMessage>) {
        // 不再使用，所有超时管理已转移到 TimeoutManager
    }

    /// 取消 FIN 超时
    /// 已迁移到 timeout_manager，此方法保留为空实现以保持兼容性
    pub fn cancel_fin_timeout(&mut self) {
        // 不再使用，所有超时管理已转移到 TimeoutManager
    }

    /// 检查是否超时
    /// 已迁移到 timeout_manager，此方法保留为空实现以保持兼容性
    pub fn is_fin_timeout(&self) -> bool {
        false // 不再使用本地超时检查
    }

    /// 检查是否需要强制关闭（兼容性方法）
    pub fn should_force_close(&self) -> bool {
        false // 所有超时逻辑已转移到 TimeoutManager
    }

    /// 标记为强制关闭
    pub fn force_close(&mut self) {
        self.cancel_fin_timeout();
        self.set_state(StreamState::Closed);
    }

    pub async fn send_data(&self, data: Bytes) -> Result<(), SessionError> {
        self.data_tx
            .send(data)
            .await
            .map_err(|_| SessionError::StreamNotFound {
                stream_id: self.info.id,
            })
    }
}

impl Drop for StreamHandle {
    fn drop(&mut self) {
        tracing::trace!(
            "StreamHandle {} dropped, sending close message to actor.",
            self.stream_id
        );
        let msg = SessionMessage::CloseStream {
            stream_id: self.stream_id,
        };

        let _ = self.crl_tx.send(msg);
    }
}
