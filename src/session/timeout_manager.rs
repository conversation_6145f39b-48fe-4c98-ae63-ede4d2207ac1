use std::collections::{BTreeMap, HashMap};
use std::sync::Arc;
use std::sync::atomic::{AtomicU64, Ordering};
use std::time::{Duration, Instant};
use tokio::sync::mpsc;
use tracing::{debug, trace, warn};

use super::SessionId;

/// 全局唯一的超时ID
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, PartialOrd, Ord)]
pub struct TimeoutId(u64);

/// 超时事件类型
#[derive(Debug, Clone)]
pub enum TimeoutEvent {
    /// 会话活动超时
    SessionActivity(SessionId),
    /// 会话心跳超时
    SessionHeartbeat(SessionId),
    /// 流FIN超时
    StreamFin(SessionId, u32),
    /// 会话状态超时
    SessionState(SessionId),
}

/// 注销通道的发送端
type DeregisterTx = mpsc::UnboundedSender<TimeoutId>;

/// TimeoutGuard 结构体，用于自动注销定时器
///
/// 当 TimeoutGuard 被 drop 时，会自动向 TimeoutManager 发送注销消息
/// 这确保了即使 SessionActor 意外退出，相关的定时器也会被正确清理
#[derive(Debug)]
pub struct TimeoutGuard {
    id: Option<TimeoutId>,
    deregister_tx: DeregisterTx,
}

impl Drop for TimeoutGuard {
    fn drop(&mut self) {
        if let Some(id) = self.id.take() {
            // 当 Guard 被丢弃时，自动发送注销消息
            // 即使发送失败也无所谓，说明 TimeoutManager 已经关闭了
            let _ = self.deregister_tx.send(id);
            trace!("TimeoutGuard dropped, deregistering timeout {:?}", id);
        }
    }
}

impl TimeoutGuard {
    /// 手动释放这个 guard（提前注销定时器）
    pub fn release(&mut self) {
        if let Some(id) = self.id.take() {
            let _ = self.deregister_tx.send(id);
            trace!(
                "TimeoutGuard manually released, deregistering timeout {:?}",
                id
            );
        }
    }

    /// 检查这个 guard 是否仍然活跃
    pub fn is_active(&self) -> bool {
        self.id.is_some()
    }
}

/// 发送给 TimeoutManager 的注册消息
#[derive(Debug)]
struct TimeoutRegistration {
    id: TimeoutId,
    deadline: Instant,
    event: TimeoutEvent,
}

/// 超时管理器句柄，用于注册定时器
#[derive(Clone)]
pub struct TimeoutManagerHandle {
    register_tx: mpsc::UnboundedSender<TimeoutRegistration>,
    deregister_tx: DeregisterTx,
    next_timeout_id: Arc<AtomicU64>,
}

impl TimeoutManagerHandle {
    /// 注册超时并返回 TimeoutGuard
    ///
    /// 这是零分配的主要接口 - 不再使用 oneshot channel
    pub fn register_timeout(&self, duration: Duration, event: TimeoutEvent) -> TimeoutGuard {
        let id = TimeoutId(self.next_timeout_id.fetch_add(1, Ordering::Relaxed));
        let deadline = Instant::now() + duration;

        let reg = TimeoutRegistration {
            id,
            deadline,
            event,
        };

        // 发送注册消息，失败也无所谓，说明 manager 已经关闭
        if self.register_tx.send(reg).is_ok() {
            trace!("Registered timeout {:?} with deadline {:?}", id, deadline);
            TimeoutGuard {
                id: Some(id),
                deregister_tx: self.deregister_tx.clone(),
            }
        } else {
            warn!("Failed to register timeout - timeout manager not running");
            // 返回一个已经失效的 guard
            TimeoutGuard {
                id: None,
                deregister_tx: self.deregister_tx.clone(),
            }
        }
    }

    /// 在指定时间点注册超时
    pub fn register_timeout_at(&self, deadline: Instant, event: TimeoutEvent) -> TimeoutGuard {
        let id = TimeoutId(self.next_timeout_id.fetch_add(1, Ordering::Relaxed));

        let reg = TimeoutRegistration {
            id,
            deadline,
            event,
        };

        if self.register_tx.send(reg).is_ok() {
            trace!("Registered timeout {:?} with deadline {:?}", id, deadline);
            TimeoutGuard {
                id: Some(id),
                deregister_tx: self.deregister_tx.clone(),
            }
        } else {
            warn!("Failed to register timeout - timeout manager not running");
            TimeoutGuard {
                id: None,
                deregister_tx: self.deregister_tx.clone(),
            }
        }
    }
}

/// 超时管理器
pub struct TimeoutManager {
    /// 按截止时间排序的超时事件
    timeouts: BTreeMap<Instant, Vec<TimeoutRegistration>>,
    /// 用于快速查找要取消的定时器
    id_to_deadline: HashMap<TimeoutId, Instant>,
    /// 注册请求接收器
    register_rx: mpsc::UnboundedReceiver<TimeoutRegistration>,
    /// 注销请求接收器
    deregister_rx: mpsc::UnboundedReceiver<TimeoutId>,
}

impl TimeoutManager {
    /// 创建新的超时管理器并返回所有组件
    pub fn new() -> (
        Self,
        TimeoutManagerHandle,
        mpsc::UnboundedSender<TimeoutEvent>,
        mpsc::UnboundedReceiver<TimeoutEvent>,
    ) {
        let (register_tx, register_rx) = mpsc::unbounded_channel();
        let (deregister_tx, deregister_rx) = mpsc::unbounded_channel();
        let (event_tx, event_rx) = mpsc::unbounded_channel();

        let manager = Self {
            timeouts: BTreeMap::new(),
            register_rx,
            deregister_rx,
            id_to_deadline: HashMap::new(),
        };

        let handle = TimeoutManagerHandle {
            register_tx,
            deregister_tx,
            next_timeout_id: Arc::new(AtomicU64::new(1)),
        };

        (manager, handle, event_tx, event_rx)
    }

    /// 运行超时管理器主循环
    pub async fn run(mut self, event_tx: mpsc::UnboundedSender<TimeoutEvent>) {
        debug!("Timeout manager started");

        loop {
            // 计算下一个定时器的睡眠时间
            let sleep_duration = if let Some((deadline, _)) = self.timeouts.first_key_value() {
                let now = Instant::now();
                if *deadline <= now {
                    // 有定时器已经到期，立即处理
                    Duration::ZERO
                } else {
                    deadline.saturating_duration_since(now)
                }
            } else {
                // 没有定时器，等待较长时间
                Duration::from_secs(3600)
            };

            tokio::select! {
                // 等待直到下一个定时器到期
                _ = tokio::time::sleep(sleep_duration) => {
                    self.fire_expired_timeouts(&event_tx).await;
                },

                // 处理新的定时器注册请求
                Some(reg) = self.register_rx.recv() => {
                    self.register_timeout_internal(reg);
                },

                // 处理定时器注销请求
                Some(id) = self.deregister_rx.recv() => {
                    self.deregister_timeout_internal(id);
                },

                // 如果所有通道都关闭，退出循环
                else => {
                    debug!("All channels closed, timeout manager shutting down");
                    break;
                }
            }
        }

        debug!("Timeout manager stopped");
    }

    /// 触发所有已过期的定时器
    async fn fire_expired_timeouts(&mut self, event_tx: &mpsc::UnboundedSender<TimeoutEvent>) {
        let now = Instant::now();
        let mut expired_registrations = Vec::new();

        // 收集所有过期的定时器
        while let Some((deadline, _)) = self.timeouts.first_key_value() {
            if *deadline > now {
                break; // BTreeMap 是排序的，后面的都不会过期
            }

            // 移除这个deadline下的所有定时器
            if let Some((deadline, registrations)) = self.timeouts.pop_first() {
                trace!(
                    "Firing {} timeouts for deadline {:?}",
                    registrations.len(),
                    deadline
                );
                expired_registrations.extend(registrations);
            }
        }

        // 处理过期的定时器
        for reg in expired_registrations {
            // 从 id_to_deadline 映射中移除
            self.id_to_deadline.remove(&reg.id);

            trace!("Timeout fired: {:?}", reg.event);

            if let Err(e) = event_tx.send(reg.event) {
                warn!("Failed to send timeout event: {:?}", e);
            }
        }
    }

    /// 内部注册超时方法
    fn register_timeout_internal(&mut self, reg: TimeoutRegistration) {
        trace!(
            "Registering timeout {:?} for deadline {:?}",
            reg.id, reg.deadline
        );

        self.id_to_deadline.insert(reg.id, reg.deadline);
        self.timeouts.entry(reg.deadline).or_default().push(reg);
    }

    /// 内部注销超时方法
    fn deregister_timeout_internal(&mut self, id: TimeoutId) {
        if let Some(deadline) = self.id_to_deadline.remove(&id) {
            trace!("Deregistering timeout {:?}", id);

            // 从 timeouts 中移除这个 id
            if let Some(registrations) = self.timeouts.get_mut(&deadline) {
                registrations.retain(|reg| reg.id != id);

                // 如果这个时间点没有其他超时了，移除整个 entry
                if registrations.is_empty() {
                    self.timeouts.remove(&deadline);
                }
            }
        }
    }
}

/// 方便函数：启动超时管理器并返回句柄和事件接收器
pub fn spawn_timeout_manager() -> (TimeoutManagerHandle, mpsc::UnboundedReceiver<TimeoutEvent>) {
    let (manager, handle, event_tx, event_rx) = TimeoutManager::new();

    // 启动管理器任务
    tokio::spawn(async move {
        manager.run(event_tx).await;
    });

    (handle, event_rx)
}

#[cfg(test)]
mod tests {
    use super::*;
    use tokio::time::Duration;

    #[tokio::test]
    async fn test_timeout_basic() {
        let (manager, handle, event_tx, mut event_rx) = TimeoutManager::new();

        // 启动管理器
        tokio::spawn(async move {
            manager.run(event_tx).await;
        });

        // 注册一个100ms的超时
        let _guard =
            handle.register_timeout(Duration::from_millis(100), TimeoutEvent::SessionActivity(1));

        // 等待超时事件
        let event = tokio::time::timeout(Duration::from_millis(200), event_rx.recv())
            .await
            .expect("Should receive timeout event")
            .expect("Should have event");

        match event {
            TimeoutEvent::SessionActivity(session_id) => {
                assert_eq!(session_id, 1);
            }
            _ => panic!("Wrong event type"),
        }
    }

    #[tokio::test]
    async fn test_timeout_guard_drop() {
        let (manager, handle, event_tx, mut event_rx) = TimeoutManager::new();

        // 启动管理器
        tokio::spawn(async move {
            manager.run(event_tx).await;
        });

        // 注册一个超时，然后立即 drop guard
        {
            let _guard = handle
                .register_timeout(Duration::from_millis(100), TimeoutEvent::SessionActivity(1));
            // guard 在这里被自动 drop
        }

        // 等待一段时间，不应该收到事件
        let result = tokio::time::timeout(Duration::from_millis(200), event_rx.recv()).await;
        assert!(
            result.is_err(),
            "Should not receive cancelled timeout event"
        );
    }

    #[tokio::test]
    async fn test_multiple_timeouts() {
        let (manager, handle, event_tx, mut event_rx) = TimeoutManager::new();

        // 启动管理器
        tokio::spawn(async move {
            manager.run(event_tx).await;
        });

        // 注册多个超时并保持 guards 活跃
        let _guard1 =
            handle.register_timeout(Duration::from_millis(50), TimeoutEvent::SessionActivity(1));

        let _guard2 =
            handle.register_timeout(Duration::from_millis(100), TimeoutEvent::SessionActivity(2));

        let _guard3 =
            handle.register_timeout(Duration::from_millis(150), TimeoutEvent::SessionActivity(3));

        // 应该按顺序收到事件
        for expected_session in [1, 2, 3] {
            let event = tokio::time::timeout(Duration::from_millis(300), event_rx.recv())
                .await
                .expect("Should receive timeout event")
                .expect("Should have event");

            match event {
                TimeoutEvent::SessionActivity(session_id) => {
                    assert_eq!(session_id, expected_session);
                }
                _ => panic!("Wrong event type"),
            }
        }
    }

    #[tokio::test]
    async fn test_guard_manual_release() {
        let (manager, handle, event_tx, mut event_rx) = TimeoutManager::new();

        // 启动管理器
        tokio::spawn(async move {
            manager.run(event_tx).await;
        });

        // 注册一个超时
        let mut guard =
            handle.register_timeout(Duration::from_millis(500), TimeoutEvent::SessionActivity(1));

        // 手动释放
        guard.release();
        assert!(!guard.is_active());

        // 等待一段时间，不应该收到事件
        let result = tokio::time::timeout(Duration::from_millis(200), event_rx.recv()).await;
        assert!(
            result.is_err(),
            "Should not receive cancelled timeout event"
        );
    }
}
