use boa::client::pool::StreamPool;
use boa::session::config::SessionConfig;
use boa::socks5::Socks5Server;
use std::sync::Arc;
use std::time::Duration;
use tracing::info;

#[global_allocator]
static ALLOC: dhat::Alloc = dhat::Alloc;

#[tokio::main]
pub async fn main() -> Result<(), Box<dyn std::error::Error>> {
    let _profiler = dhat::Profiler::new_heap();
    tracing_subscriber::fmt()
        .with_max_level(tracing::Level::DEBUG) // 降低到 INFO 级别，减少 rustls 的 DEBUG 日志
        .with_target(false)
        .init();

    info!("Starting BOA SOCKS5 Proxy Server...");

    // 1. 配置远程服务器连接
    let remote_config = SessionConfig::new(
        "127.0.0.1:25565".to_string(), // 远程服务器地址
        "127.0.0.1".to_string(),       // SNI 服务器名称
        "Gg980425==".to_string(),      // 认证密码
        3,
    )
    .with_fin_timeout(Duration::from_secs(5)); // 减少FIN超时到10秒

    // 2. 创建连接池
    let pool = StreamPool::new(remote_config).with_max_streams_per_session(50);

    let pool = Arc::new(pool);

    info!("StreamPool created with min 3 sessions, max 50 streams per session");

    // 3. SOCKS5 服务器配置
    let socks5_bind_addr = "127.0.0.1:1080";
    let auth = None;

    // 4. 创建并启动 SOCKS5 服务器
    info!("Creating SOCKS5 server on {}", socks5_bind_addr);
    let server = Socks5Server::bind(socks5_bind_addr, pool, auth).await?;

    info!("SOCKS5 proxy server listening on {}", socks5_bind_addr);
    info!(
        "You can now configure your applications to use SOCKS5 proxy at {}",
        socks5_bind_addr
    );

    // 注册ctrl+c信号处理
    let (shutdown_tx, shutdown_rx) = tokio::sync::oneshot::channel();

    tokio::spawn(async move {
        tokio::signal::ctrl_c().await.unwrap();
        info!("Ctrl+C received, shutting down...");
        let _ = shutdown_tx.send(());
    });

    tokio::select! {
        _ = server.run() => {}
        _ = shutdown_rx => {
            info!("Shutting down...");
        }
    }

    drop(_profiler); // 在所有任务结束后再 drop profiler
    Ok(())
}
