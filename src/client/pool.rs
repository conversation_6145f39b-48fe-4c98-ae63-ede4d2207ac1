use crate::SockAddr;
use crate::session::config::SessionConfig;
use crate::session::inner::{SessionActor, SessionError, SessionHandle};
use crate::session::stream::StreamHandle;
use crate::utils::create_rustls_client_connection_with_options;
use bytes::Bytes;
use parking_lot::RwLock;
use sha2::{Digest, Sha256};
use std::collections::HashMap;
use std::sync::Arc;
use std::sync::atomic::{AtomicU32, AtomicUsize, Ordering};
use std::time::{Duration, Instant};
use tokio::sync::watch;
use tokio::task::Join<PERSON><PERSON><PERSON>;
use tracing::{debug, error, info, warn};

// Configuration constants
const SCAVENGER_INTERVAL: Duration = Duration::from_secs(30);
const MAX_IDLE_DURATION: Duration = Duration::from_secs(60);

/// A managed session with sequence number and lifecycle tracking
#[derive(Clone)]
struct ManagedSession {
    seq: usize,
    handle: SessionHandle,
    /// Number of active streams on this session (lock-free)
    active_streams: Arc<AtomicU32>,
    /// Maximum number of concurrent streams this session can handle
    max_streams: u32,
}

impl ManagedSession {
    fn new(seq: usize, handle: SessionHandle, max_streams: u32) -> Self {
        Self {
            seq,
            handle,
            active_streams: Arc::new(AtomicU32::new(0)),
            max_streams,
        }
    }

    /// Check if this session can accept more streams
    fn can_accept_stream(&self) -> bool {
        let active = self.active_streams.load(Ordering::Relaxed);
        active < self.max_streams && !self.handle.is_cancelled()
    }

    /// Try to open a stream on this session
    async fn try_open_stream(&self, target: SockAddr) -> Result<PooledStream, SessionError> {
        // Check if we can accept more streams
        let current_active = self.active_streams.load(Ordering::Relaxed);
        if current_active >= self.max_streams {
            return Err(SessionError::TooManyStreams);
        }

        // Try to open the stream first
        match self.handle.open_stream(target).await {
            Ok(stream) => {
                // Only increment on success, the PooledStream will decrement on drop
                self.active_streams.fetch_add(1, Ordering::Relaxed);
                Ok(PooledStream::new(stream, self.active_streams.clone()))
            }
            Err(e) => Err(e),
        }
    }

    fn id(&self) -> crate::session::inner::SessionId {
        self.handle.id()
    }

    fn is_cancelled(&self) -> bool {
        self.handle.is_cancelled()
    }

    /// Check if session is completely idle (no active streams)
    fn is_idle(&self) -> bool {
        self.active_streams.load(Ordering::Relaxed) == 0 && !self.is_cancelled()
    }
}

/// Represents an idle session with its idle start time
struct IdleSession {
    session: ManagedSession,
    idle_since: Instant,
}

/// A stream with automatic cleanup when dropped
pub struct PooledStream {
    stream: Option<StreamHandle>,
    active_counter: Arc<AtomicU32>,
}

impl PooledStream {
    fn new(stream: StreamHandle, active_counter: Arc<AtomicU32>) -> Self {
        Self {
            stream: Some(stream),
            active_counter,
        }
    }

    pub fn stream_id(&self) -> u32 {
        self.stream.as_ref().unwrap().stream_id()
    }

    /// 消耗 self，拆出两个 owned halves
    pub fn into_owned_split(
        mut self,
    ) -> (
        tokio::io::ReadHalf<StreamHandle>,
        tokio::io::WriteHalf<StreamHandle>,
    ) {
        // 取出 TcpStream
        let tcp = self.stream.take().expect("PooledStream 已经被拆分过");
        // 这里直接消费掉 tcp，把它拆成 OwnedReadHalf/OwnedWriteHalf
        tokio::io::split(tcp)
    }
}

impl Drop for PooledStream {
    fn drop(&mut self) {
        if self.stream.take().is_some() {
            self.active_counter.fetch_sub(1, Ordering::Relaxed);
        }
    }
}

impl std::ops::Deref for PooledStream {
    type Target = StreamHandle;

    fn deref(&self) -> &Self::Target {
        self.stream.as_ref().unwrap()
    }
}

impl std::ops::DerefMut for PooledStream {
    fn deref_mut(&mut self) -> &mut Self::Target {
        self.stream.as_mut().unwrap()
    }
}

/// Internal state for session creation coordination
struct SessionCreation {
    notify: watch::Sender<Option<Result<ManagedSession, SessionError>>>,
}

/// Thread-safe, clonable stream pool with intelligent session lifecycle management
#[derive(Clone)]
pub struct StreamPool {
    config: Arc<SessionConfig>,
    /// Active sessions that can accept new streams (by seq number)
    active_sessions: Arc<RwLock<HashMap<usize, ManagedSession>>>,
    /// Idle sessions available for reuse
    idle_sessions: Arc<parking_lot::Mutex<Vec<IdleSession>>>,
    /// Ongoing session creation processes
    creating_sessions: Arc<parking_lot::Mutex<HashMap<String, SessionCreation>>>,
    /// Sequence number generator for sessions
    next_seq: Arc<AtomicUsize>,
    /// Maximum streams per session
    max_streams_per_session: u32,
    /// Background scavenger task handle
    _scavenger_handle: Arc<JoinHandle<()>>,
}

impl StreamPool {
    /// Creates a new stream pool for a specific server configuration
    pub fn new(config: SessionConfig) -> Self {
        let pool = Self {
            config: Arc::new(config),
            active_sessions: Arc::new(RwLock::new(HashMap::new())),
            idle_sessions: Arc::new(parking_lot::Mutex::new(Vec::new())),
            creating_sessions: Arc::new(parking_lot::Mutex::new(HashMap::new())),
            next_seq: Arc::new(AtomicUsize::new(1)),
            max_streams_per_session: 100,
            _scavenger_handle: Arc::new(tokio::spawn(async {})), // Placeholder
        };

        // Start background scavenger task
        let scavenger_pool = pool.clone();
        let scavenger_handle = tokio::spawn(async move {
            let mut interval = tokio::time::interval(SCAVENGER_INTERVAL);
            loop {
                interval.tick().await;
                scavenger_pool.scavenge_idle_sessions().await;
                scavenger_pool.move_idle_active_sessions_to_idle().await;
                scavenger_pool.ensure_minimum_sessions().await;
            }
        });

        // Replace the placeholder handle
        unsafe {
            let handle_ptr = Arc::as_ptr(&pool._scavenger_handle) as *mut JoinHandle<()>;
            std::ptr::write(handle_ptr, scavenger_handle);
        }

        pool
    }

    /// Configure the maximum number of streams per session
    pub fn with_max_streams_per_session(mut self, max_streams: u32) -> Self {
        self.max_streams_per_session = max_streams;
        self
    }

    /// Get a stream from the pool
    pub async fn get_stream(&self, target: SockAddr) -> Result<PooledStream, SessionError> {
        loop {
            // 0. Check if we should create a new session due to min_session_count
            let total_sessions = {
                let active_sessions = self.active_sessions.read();
                let idle_sessions = self.idle_sessions.lock();
                active_sessions.len() + idle_sessions.len()
            };

            if total_sessions < self.config.min_session_count {
                // Below minimum threshold, create new session directly
                let session = self.get_or_create_session().await?;
                match session.try_open_stream(target).await {
                    Ok(stream) => {
                        // Add to active sessions
                        self.move_session_to_active(session.clone()).await;

                        info!(
                            "Opened stream {} on new session {} (seq={}) - below min threshold ({}/{})",
                            stream.stream_id(),
                            session.id(),
                            session.seq,
                            total_sessions + 1,
                            self.config.min_session_count
                        );
                        return Ok(stream);
                    }
                    Err(e) => {
                        error!(
                            "Failed to open stream on new session {} (seq={}): {}",
                            session.id(),
                            session.seq,
                            e
                        );
                        return Err(e);
                    }
                }
            }

            // 1. Check idle sessions first - prioritize highest Seq (most recent)
            if let Some(session) = self.get_idle_session_with_highest_seq() {
                match session.try_open_stream(target.clone()).await {
                    Ok(stream) => {
                        // Move session to active pool
                        self.move_session_to_active(session.clone()).await;

                        debug!(
                            "Opened stream {} on reused session {} (seq={})",
                            stream.stream_id(),
                            session.id(),
                            session.seq
                        );

                        return Ok(stream);
                    }
                    Err(e) => {
                        warn!(
                            "Failed to open stream on idle session {} (seq={}): {}, removing",
                            session.id(),
                            session.seq,
                            e
                        );
                        self.remove_session_completely(session.seq).await;
                        continue;
                    }
                }
            }

            // 2. Check active sessions - prioritize highest Seq
            if let Some(session) = self.find_available_active_session() {
                match session.try_open_stream(target.clone()).await {
                    Ok(stream) => {
                        debug!(
                            "Opened stream {} on active session {} (seq={})",
                            stream.stream_id(),
                            session.id(),
                            session.seq
                        );
                        return Ok(stream);
                    }
                    Err(SessionError::TooManyStreams) => {
                        debug!(
                            "Session {} (seq={}) is full, trying next",
                            session.id(),
                            session.seq
                        );
                        continue;
                    }
                    Err(e) => {
                        warn!(
                            "Session {} (seq={}) failed to open stream: {}, removing",
                            session.id(),
                            session.seq,
                            e
                        );
                        self.remove_session_completely(session.seq).await;
                        continue;
                    }
                }
            }

            // 3. No available sessions, create a new one
            let session = self.get_or_create_session().await?;
            match session.try_open_stream(target).await {
                Ok(stream) => {
                    // Add to active sessions
                    self.move_session_to_active(session.clone()).await;

                    info!(
                        "Opened stream {} on new session {} (seq={})",
                        stream.stream_id(),
                        session.id(),
                        session.seq
                    );
                    return Ok(stream);
                }
                Err(e) => {
                    error!(
                        "Failed to open stream on new session {} (seq={}): {}",
                        session.id(),
                        session.seq,
                        e
                    );
                    return Err(e);
                }
            }
        }
    }

    /// Get idle session with load balancing strategy
    fn get_idle_session_with_highest_seq(&self) -> Option<ManagedSession> {
        let mut idle_sessions = self.idle_sessions.lock();
        if idle_sessions.is_empty() {
            return None;
        }

        // 负载均衡策略：优先选择最少使用的会话，然后是最新的
        let mut best_index = 0;
        let mut min_active_streams = u32::MAX;
        let mut max_seq = 0;

        for (i, idle) in idle_sessions.iter().enumerate() {
            if idle.session.is_cancelled() {
                continue;
            }

            let active_count = idle.session.active_streams.load(Ordering::Relaxed);

            // 优先选择活跃流数最少的会话
            if active_count < min_active_streams
                || (active_count == min_active_streams && idle.session.seq > max_seq)
            {
                min_active_streams = active_count;
                max_seq = idle.session.seq;
                best_index = i;
            }
        }

        if !idle_sessions[best_index].session.is_cancelled() {
            debug!(
                "Selected idle session {} (seq={}) with {} active streams",
                idle_sessions[best_index].session.id(),
                idle_sessions[best_index].session.seq,
                min_active_streams
            );
            Some(idle_sessions.remove(best_index).session)
        } else {
            None
        }
    }

    /// Find available active session with load balancing
    fn find_available_active_session(&self) -> Option<ManagedSession> {
        let active_sessions = self.active_sessions.read();

        // 负载均衡：选择活跃流数最少的会话
        active_sessions
            .values()
            .filter(|session| session.can_accept_stream())
            .min_by_key(|session| session.active_streams.load(Ordering::Relaxed))
            .cloned()
    }

    /// Move session from idle to active pool
    async fn move_session_to_active(&self, session: ManagedSession) {
        let mut active_sessions = self.active_sessions.write();
        active_sessions.insert(session.seq, session);
    }

    /// Remove session completely from both active and idle pools
    async fn remove_session_completely(&self, session_seq: usize) {
        // Check if we can remove this session without going below minimum
        // Only count healthy (non-cancelled) sessions toward the minimum
        let can_remove = {
            let active_sessions = self.active_sessions.read();
            let idle_sessions = self.idle_sessions.lock();

            let healthy_active_count = active_sessions
                .values()
                .filter(|session| !session.is_cancelled())
                .count();
            let healthy_idle_count = idle_sessions
                .iter()
                .filter(|idle| !idle.session.is_cancelled())
                .count();

            let current_healthy_total = healthy_active_count + healthy_idle_count;

            // If the session we're trying to remove is cancelled, we can always remove it
            // regardless of minimum count since it doesn't contribute to healthy sessions
            let session_is_cancelled = {
                if let Some(session) = active_sessions.get(&session_seq) {
                    session.is_cancelled()
                } else {
                    idle_sessions
                        .iter()
                        .find(|idle| idle.session.seq == session_seq)
                        .map(|idle| idle.session.is_cancelled())
                        .unwrap_or(false)
                }
            };

            session_is_cancelled || current_healthy_total > self.config.min_session_count
        };

        if !can_remove {
            // Find the actual session ID for better logging
            let session_id = {
                let active_sessions = self.active_sessions.read();
                if let Some(session) = active_sessions.get(&session_seq) {
                    Some(session.id())
                } else {
                    let idle_sessions = self.idle_sessions.lock();
                    idle_sessions
                        .iter()
                        .find(|idle| idle.session.seq == session_seq)
                        .map(|idle| idle.session.id())
                }
            };

            if let Some(id) = session_id {
                warn!(
                    "Cannot remove session {} (seq={}): would violate minimum session count ({})",
                    id, session_seq, self.config.min_session_count
                );
            } else {
                warn!(
                    "Cannot remove session with seq={}: would violate minimum session count ({})",
                    session_seq, self.config.min_session_count
                );
            }
            return;
        }

        // Remove from active sessions
        {
            let mut active_sessions = self.active_sessions.write();
            if let Some(session) = active_sessions.remove(&session_seq) {
                debug!(
                    "Removed session {} (seq={}) from active pool",
                    session.id(),
                    session.seq
                );
            }
        }

        // Remove from idle sessions
        {
            let mut idle_sessions = self.idle_sessions.lock();
            idle_sessions.retain(|idle| idle.session.seq != session_seq);
        }
    }

    /// Background task to clean up old idle sessions
    async fn scavenge_idle_sessions(&self) {
        let sessions_to_remove = {
            // First, calculate healthy sessions across both pools
            let active_sessions = self.active_sessions.read();
            let mut idle_sessions = self.idle_sessions.lock();

            if idle_sessions.is_empty() {
                return;
            }

            let healthy_active_count = active_sessions
                .values()
                .filter(|session| !session.is_cancelled())
                .count();
            let healthy_idle_count = idle_sessions
                .iter()
                .filter(|idle| !idle.session.is_cancelled())
                .count();
            let current_healthy_sessions = healthy_active_count + healthy_idle_count;
            let now = Instant::now();

            // Calculate how many sessions we can remove without going below min_session_count
            let max_removable =
                current_healthy_sessions.saturating_sub(self.config.min_session_count);

            if max_removable == 0 {
                debug!(
                    "Cannot remove any idle sessions: current healthy {} <= min required {}",
                    current_healthy_sessions, self.config.min_session_count
                );
                return;
            }

            // Collect sessions that need to be removed, but respect the min_session_count limit
            let mut to_remove = Vec::new();
            let mut removed_count = 0;

            idle_sessions.retain(|idle| {
                // Always remove cancelled/terminated sessions regardless of minimum count
                if idle.session.is_cancelled() {
                    warn!(
                        "Removing terminated idle session {} (seq={})",
                        idle.session.id(),
                        idle.session.seq
                    );
                    to_remove.push(idle.session.seq);
                    return false;
                }

                if removed_count >= max_removable {
                    return true; // Keep remaining healthy sessions to maintain minimum count
                }

                if now.duration_since(idle.idle_since) > MAX_IDLE_DURATION {
                    warn!(
                        "Scavenging idle session {} (seq={}) idle for {}s (maintaining min sessions: {}/{})",
                        idle.session.id(),
                        idle.session.seq,
                        now.duration_since(idle.idle_since).as_secs(),
                        current_healthy_sessions - removed_count - 1,
                        self.config.min_session_count
                    );
                    to_remove.push(idle.session.seq);
                    removed_count += 1;
                    false
                } else {
                    true
                }
            });

            if removed_count > 0 {
                info!(
                    "Scavenged {} old idle sessions (maintaining minimum {} sessions, total now: {})",
                    removed_count,
                    self.config.min_session_count,
                    current_healthy_sessions - removed_count
                );
            }

            to_remove
        };

        // Shutdown sessions outside the lock
        for session_seq in sessions_to_remove {
            // Find the session to shutdown
            let session_handle = {
                let active_sessions = self.active_sessions.read();
                if let Some(session) = active_sessions.get(&session_seq) {
                    Some(session.handle.clone())
                } else {
                    let idle_sessions = self.idle_sessions.lock();
                    idle_sessions
                        .iter()
                        .find(|idle| idle.session.seq == session_seq)
                        .map(|idle| idle.session.handle.clone())
                }
            };

            if let Some(handle) = session_handle {
                let _ = handle.shutdown().await;
            }
        }
    }

    /// Move active sessions that have become idle to the idle pool
    async fn move_idle_active_sessions_to_idle(&self) {
        let idle_sessions_to_move: Vec<ManagedSession> = {
            let active_sessions = self.active_sessions.read();
            active_sessions
                .values()
                .filter(|session| session.is_idle())
                .cloned()
                .collect()
        };

        if !idle_sessions_to_move.is_empty() {
            // Remove from active sessions
            {
                let mut active_sessions = self.active_sessions.write();
                for session in &idle_sessions_to_move {
                    active_sessions.remove(&session.seq);
                }
            }

            // Add to idle sessions
            {
                let mut idle_sessions = self.idle_sessions.lock();
                let now = Instant::now();
                for session in idle_sessions_to_move {
                    debug!(
                        "Moving session {} (seq={}) from active to idle pool",
                        session.id(),
                        session.seq
                    );
                    idle_sessions.push(IdleSession {
                        session,
                        idle_since: now,
                    });
                }
            }
        }
    }

    /// Ensure we maintain the minimum number of sessions
    async fn ensure_minimum_sessions(&self) {
        let current_total_sessions = {
            let active_sessions = self.active_sessions.read();
            let idle_sessions = self.idle_sessions.lock();
            active_sessions.len() + idle_sessions.len()
        };

        if current_total_sessions < self.config.min_session_count {
            let sessions_needed = self.config.min_session_count - current_total_sessions;
            info!(
                "Current session count ({}) below minimum ({}), creating {} additional sessions",
                current_total_sessions, self.config.min_session_count, sessions_needed
            );

            let mut tasks = Vec::new();
            for _ in 0..sessions_needed {
                let pool = self.clone();
                tasks.push(tokio::spawn(async move {
                    match pool.create_session().await {
                        Ok(session) => {
                            let mut idle_sessions = pool.idle_sessions.lock();
                            idle_sessions.push(IdleSession {
                                session: session.clone(),
                                idle_since: Instant::now(),
                            });
                            info!(
                                "Created minimum session {} (seq={}) and added to idle pool",
                                session.id(),
                                session.seq
                            );
                            Ok(())
                        }
                        Err(e) => {
                            error!("Failed to create minimum session: {}", e);
                            Err(e)
                        }
                    }
                }));
            }

            for task in tasks {
                if let Err(e) = task.await {
                    error!("Session creation task failed: {}", e);
                }
            }
        }
    }

    /// Get or create a session
    async fn get_or_create_session(&self) -> Result<ManagedSession, SessionError> {
        let server_key = self.config.server_addr.clone();

        // Check if someone is already creating a session
        let waiter = {
            let mut creating = self.creating_sessions.lock();
            if let Some(creation) = creating.get(&server_key) {
                Some(creation.notify.subscribe())
            } else {
                let (tx, _rx) = watch::channel(None);
                creating.insert(server_key.clone(), SessionCreation { notify: tx.clone() });

                // Start creation in background
                let pool_clone = self.clone();
                let notify_clone = tx.clone();
                tokio::spawn(async move {
                    let result = pool_clone.create_session().await;
                    match result {
                        Ok(session) => {
                            info!("Created session {} (seq={})", session.id(), session.seq);
                            let _ = notify_clone.send(Some(Ok(session)));
                        }
                        Err(e) => {
                            error!("Failed to create session: {}", e);
                            let _ = notify_clone.send(Some(Err(SessionError::CreationFailed)));
                        }
                    }

                    pool_clone.creating_sessions.lock().remove(&server_key);
                });

                None
            }
        };

        if let Some(mut waiter) = waiter {
            // We are a follower, wait for the leader to create the session
            loop {
                if waiter.changed().await.is_err() {
                    return Err(SessionError::CreationFailed);
                }

                if let Some(result) = waiter.borrow().as_ref() {
                    match result {
                        Ok(session) => return Ok(session.clone()),
                        Err(_) => return Err(SessionError::CreationFailed),
                    }
                }
            }
        } else {
            // We are the leader, we started the creation task, now wait for the result
            // We need to wait for our own background task to complete and get the result
            let server_key = self.config.server_addr.clone();

            // Get a subscriber to wait for the result
            let result_waiter = {
                let creating = self.creating_sessions.lock();
                creating
                    .get(&server_key)
                    .map(|creation| creation.notify.subscribe())
            };

            if let Some(mut result_waiter) = result_waiter {
                loop {
                    if result_waiter.changed().await.is_err() {
                        return Err(SessionError::CreationFailed);
                    }

                    if let Some(result) = result_waiter.borrow().as_ref() {
                        match result {
                            Ok(session) => return Ok(session.clone()),
                            Err(_) => return Err(SessionError::CreationFailed),
                        }
                    }
                }
            } else {
                // Creation task completed but we didn't get the result
                // This shouldn't happen in normal cases
                Err(SessionError::CreationFailed)
            }
        }
    }

    /// Create a new session
    async fn create_session(&self) -> Result<ManagedSession, SessionError> {
        let seq = self.next_seq.fetch_add(1, Ordering::Relaxed);
        info!("Creating session with seq={}", seq);

        // Create TLS connection
        let tls_stream = create_rustls_client_connection_with_options(
            &self.config.server_addr,
            &self.config.server_name,
            self.config.skip_verify,
        )
        .await
        .map_err(SessionError::IoError)?;

        // Calculate password hash
        let mut hasher = Sha256::new();
        hasher.update(self.config.password.as_bytes());
        let password_hash = hasher.finalize();
        let password_hash = Bytes::from(password_hash.to_vec());

        // Create timeout manager for this session
        let (timeout_handle, timeout_event_rx) = crate::session::create_timeout_manager();

        // Start Session Actor
        let mut session_handle = SessionActor::run_structured(
            tokio_rustls::TlsStream::Client(tls_stream),
            password_hash,
            timeout_handle,
            timeout_event_rx,
        )
        .await?;

        // Wait for session to become active
        session_handle.wait_for_active().await?;

        let managed_session =
            ManagedSession::new(seq, session_handle, self.max_streams_per_session);
        info!(
            "Successfully created session {} (seq={})",
            managed_session.id(),
            seq
        );

        Ok(managed_session)
    }

    /// Returns the configuration this pool is connected to
    pub fn config(&self) -> &SessionConfig {
        &self.config
    }

    /// Get statistics about the pool
    pub fn stats(&self) -> PoolStats {
        let active_sessions = self.active_sessions.read();
        let idle_sessions = self.idle_sessions.lock();

        let total_sessions = active_sessions.len() + idle_sessions.len();
        let total_active_streams: u32 = active_sessions
            .values()
            .map(|s| s.active_streams.load(Ordering::Relaxed))
            .sum();

        PoolStats {
            total_sessions,
            active_sessions: active_sessions.len(),
            idle_sessions: idle_sessions.len(),
            total_active_streams,
            max_streams_per_session: self.max_streams_per_session,
        }
    }
}

/// Statistics about the stream pool
#[derive(Debug, Clone)]
pub struct PoolStats {
    pub total_sessions: usize,
    pub active_sessions: usize,
    pub idle_sessions: usize,
    pub total_active_streams: u32,
    pub max_streams_per_session: u32,
}
