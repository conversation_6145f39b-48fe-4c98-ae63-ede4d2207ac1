use bytes::Bytes;
use object_pool::{Pool, Reusable};
use std::ops::{Deref, DerefMut};
use std::sync::LazyLock;

/// 最大帧大小：u16 max (65535) + 帧头 (7) = 65542 字节
pub const MAX_FRAME_SIZE: usize = 65535 + 7;

/// 帧缓冲区类型
pub type FrameBuffer = Box<[u8; MAX_FRAME_SIZE]>;

/// 全局帧缓冲区池
static FRAME_BUFFER_POOL: LazyLock<Pool<FrameBuffer>> = LazyLock::new(|| {
    Pool::new(8, || { // 减少从 50 到 8，每个 65KB，总共约 520KB
        // 创建固定大小的帧缓冲区
        Box::new([0u8; MAX_FRAME_SIZE])
    })
});

/// 池化的帧缓冲区，实现了自动归还机制
pub struct PooledFrameBuffer {
    buffer: Reusable<'static, FrameBuffer>,
    /// 实际使用的数据长度
    len: usize,
}

impl std::fmt::Debug for PooledFrameBuffer {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        f.debug_struct("PooledFrameBuffer")
            .field("len", &self.len)
            .field("capacity", &MAX_FRAME_SIZE)
            .finish()
    }
}

impl PooledFrameBuffer {
    /// 从池中获取一个缓冲区
    pub fn new() -> Self {
        Self {
            buffer: FRAME_BUFFER_POOL.pull(|| Box::new([0u8; 65542])),
            len: 0,
        }
    }

    /// 设置实际使用的数据长度
    pub fn set_len(&mut self, len: usize) {
        debug_assert!(len <= MAX_FRAME_SIZE, "Length exceeds buffer size");
        self.len = len;
    }

    /// 获取实际使用的数据长度
    pub fn len(&self) -> usize {
        self.len
    }

    /// 检查是否为空
    pub fn is_empty(&self) -> bool {
        self.len == 0
    }

    /// 获取实际使用的数据切片
    pub fn as_slice(&self) -> &[u8] {
        &self.buffer[..self.len]
    }

    /// 获取可变的数据切片（用于写入数据）
    pub fn as_mut_slice(&mut self) -> &mut [u8] {
        &mut self.buffer[..self.len]
    }

    pub fn into_bytes(self) -> Bytes {
        if self.len == 0 {
            return Bytes::new();
        }

        struct Owner(Reusable<'static, FrameBuffer>, usize);

        impl AsRef<[u8]> for Owner {
            fn as_ref(&self) -> &[u8] {
                &self.0[..self.1]
            }
        }

        let owner = Owner(self.buffer, self.len);
        Bytes::from_owner(owner)
    }

    /// 从源缓冲区拷贝数据到池化缓冲区
    pub fn copy_from_slice(&mut self, src: &[u8]) {
        debug_assert!(src.len() <= MAX_FRAME_SIZE, "Source data too large");
        let copy_len = src.len().min(MAX_FRAME_SIZE);
        self.buffer[..copy_len].copy_from_slice(&src[..copy_len]);
        self.len = copy_len;
    }

    /// 获取缓冲区的最大容量
    pub fn capacity(&self) -> usize {
        MAX_FRAME_SIZE
    }
}

impl Default for PooledFrameBuffer {
    fn default() -> Self {
        Self::new()
    }
}

impl Deref for PooledFrameBuffer {
    type Target = [u8];

    fn deref(&self) -> &Self::Target {
        &self.buffer[..self.len]
    }
}

impl DerefMut for PooledFrameBuffer {
    fn deref_mut(&mut self) -> &mut Self::Target {
        &mut self.buffer[..self.len]
    }
}

impl AsRef<[u8]> for PooledFrameBuffer {
    fn as_ref(&self) -> &[u8] {
        &self.buffer[..self.len]
    }
}

impl AsMut<[u8]> for PooledFrameBuffer {
    fn as_mut(&mut self) -> &mut [u8] {
        &mut self.buffer[..self.len]
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_pooled_buffer_basic() {
        let mut buffer = PooledFrameBuffer::new();
        assert_eq!(buffer.len(), 0);
        assert!(buffer.is_empty());
        assert_eq!(buffer.capacity(), MAX_FRAME_SIZE);

        let test_data = b"Hello, World!";
        buffer.copy_from_slice(test_data);
        assert_eq!(buffer.len(), test_data.len());
        assert_eq!(buffer.as_slice(), test_data);
    }

    #[test]
    fn test_into_bytes() {
        let mut buffer = PooledFrameBuffer::new();
        let test_data = b"Test data for conversion";
        buffer.copy_from_slice(test_data);

        let bytes = buffer.into_bytes();
        assert_eq!(bytes.as_ref(), test_data);
    }

    #[test]
    fn test_buffer_reuse() {
        // 创建并使用一个缓冲区
        {
            let mut buffer1 = PooledFrameBuffer::new();
            buffer1.copy_from_slice(b"test");
        } // buffer1 在这里被 drop，应该归还给池

        // 创建另一个缓冲区，应该复用之前的内存
        let buffer2 = PooledFrameBuffer::new();
        assert_eq!(buffer2.capacity(), MAX_FRAME_SIZE);
    }
}
