use crate::protocol::command::Command;
use crate::protocol::frame_pool::<PERSON><PERSON><PERSON><PERSON>eBuffer;
use bytes::{Buf, BufMut, Bytes, BytesMut};
use std::io::{self, Cursor};

#[derive(Debug, <PERSON><PERSON>, PartialEq)]
pub struct Frame {
    pub command: Command,
    pub stream_id: u32,
    pub data: Bytes,
}

/// 池化版本的帧，使用固定大小内存池避免动态分配
#[derive(Debug)]
pub struct PooledFrame {
    pub command: Command,
    pub stream_id: u32,
    pub data: PooledFrameBuffer,
}

impl PooledFrame {
    /// 创建新的池化帧
    pub fn new(command: Command, stream_id: u32) -> Self {
        Self {
            command,
            stream_id,
            data: PooledFrameBuffer::new(),
        }
    }

    /// 将池化帧转换为普通帧（消费 PooledFrame）
    pub fn into_frame(self) -> Frame {
        Frame {
            command: self.command,
            stream_id: self.stream_id,
            data: self.data.into_bytes(),
        }
    }

    /// 获取数据长度
    pub fn data_len(&self) -> usize {
        self.data.len()
    }
}

pub const FRAME_HEADER_SIZE: usize = 7; // command(1) + stream_id(4) + data_length(2)
impl Frame {
    pub fn encode(&self) -> Bytes {
        let mut buf = BytesMut::with_capacity(FRAME_HEADER_SIZE + self.data.len());
        buf.put_u8(self.command as u8);
        buf.put_u32(self.stream_id);
        buf.put_u16(self.data.len() as u16);
        buf.put_slice(&self.data);
        buf.freeze()
    }

    /// 将帧编码到提供的缓冲区中，避免重复分配
    pub fn encode_to_buffer<B: BufMut>(&self, buf: &mut B) {
        buf.put_u8(self.command as u8);
        buf.put_u32(self.stream_id);
        buf.put_u16(self.data.len() as u16);
        buf.put_slice(&self.data);
    }


    pub fn decode(buf: &mut Cursor<&[u8]>) -> io::Result<Option<Frame>> {
        if buf.remaining() < FRAME_HEADER_SIZE {
            return Ok(None);
        }

        let command = Command::from_u8(buf.get_u8())?;
        let stream_id = buf.get_u32();
        let data_len = buf.get_u16() as usize;

        if buf.remaining() < data_len {
            return Ok(None);
        }

        let mut data = vec![0; data_len];
        buf.copy_to_slice(&mut data);

        Ok(Some(Frame {
            command,
            stream_id,
            data: Bytes::from(data),
        }))
    }


    pub fn try_parse_pooled(buf: &[u8]) -> io::Result<Option<(PooledFrame, usize)>> {
        if buf.len() < FRAME_HEADER_SIZE {
            return Ok(None);
        }

        // 一次性读取头部，避免多次数组访问
        let header = &buf[..FRAME_HEADER_SIZE];
        let command = Command::from_u8(header[0])?;
        let stream_id = u32::from_be_bytes([header[1], header[2], header[3], header[4]]);
        let data_len = u16::from_be_bytes([header[5], header[6]]) as usize;

        // 检查是否有完整的帧
        let total_len = FRAME_HEADER_SIZE + data_len;
        if buf.len() < total_len {
            return Ok(None);
        }

        // 从池中获取缓冲区
        let mut pooled_buffer = PooledFrameBuffer::new();

        // 只有在有数据时才拷贝
        if data_len > 0 {
            let data_start = FRAME_HEADER_SIZE;
            let data_end = data_start + data_len;

            // 将数据拷贝到池化缓冲区中
            pooled_buffer.copy_from_slice(&buf[data_start..data_end]);
        }

        let pooled_frame = PooledFrame {
            command,
            stream_id,
            data: pooled_buffer,
        };

        // 返回池化帧和消耗的总字节数
        Ok(Some((pooled_frame, total_len)))
    }
}
