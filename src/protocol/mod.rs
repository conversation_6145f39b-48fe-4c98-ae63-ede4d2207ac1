pub mod auth;
pub mod command;
pub mod frame;
pub mod frame_pool;
pub mod padding;

use bytes::{Buf, BufMut, Bytes, BytesMut};
use std::fmt;
use std::net::{IpAddr, Ipv4Addr, Ipv6Addr};
use std::str::FromStr;
use thiserror::Error;

#[derive(Debug, Clone, PartialEq, Eq, Hash)]
pub enum Address {
    /// IPv4 地址
    Ipv4(Ipv4Addr),
    /// IPv6 地址
    Ipv6(Ipv6Addr),
    /// 域名
    Domain(String),
}

#[derive(Debug, Clone, PartialEq, Eq, Hash)]
pub struct SockAddr {
    pub address: Address,
    pub port: u16,
}

#[derive(Debug, Error)]
pub enum AddressError {
    #[error("Invalid IP address: {0}")]
    InvalidIpAddress(String),
    #[error("Invalid domain name: {0}")]
    InvalidDomain(String),
    #[error("Invalid port number: {0}")]
    InvalidPort(u16),
    #[error("Invalid address format: {0}")]
    InvalidFormat(String),
}

impl Address {
    /// 从字符串解析地址
    pub fn parse(addr: &str) -> Result<Self, AddressError> {
        // 尝试解析为 IP 地址
        if let Ok(ip) = IpAddr::from_str(addr) {
            return Ok(match ip {
                IpAddr::V4(ipv4) => Address::Ipv4(ipv4),
                IpAddr::V6(ipv6) => Address::Ipv6(ipv6),
            });
        }

        // 验证域名格式
        if Self::is_valid_domain(addr) {
            Ok(Address::Domain(addr.to_string()))
        } else {
            Err(AddressError::InvalidFormat(addr.to_string()))
        }
    }

    /// 验证域名格式
    fn is_valid_domain(domain: &str) -> bool {
        if domain.is_empty() || domain.len() > 253 {
            return false;
        }

        // 基本的域名验证
        let parts: Vec<&str> = domain.split('.').collect();
        if parts.is_empty() {
            return false;
        }

        for part in parts {
            if part.is_empty() || part.len() > 63 {
                return false;
            }

            // 检查字符是否合法
            if !part.chars().all(|c| c.is_ascii_alphanumeric() || c == '-') {
                return false;
            }

            // 不能以连字符开头或结尾
            if part.starts_with('-') || part.ends_with('-') {
                return false;
            }
        }

        true
    }

    /// 检查是否为 IP 地址
    pub fn is_ip(&self) -> bool {
        matches!(self, Address::Ipv4(_) | Address::Ipv6(_))
    }

    /// 检查是否为域名
    pub fn is_domain(&self) -> bool {
        matches!(self, Address::Domain(_))
    }
}

impl fmt::Display for Address {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            Address::Ipv4(ip) => write!(f, "{}", ip),
            Address::Ipv6(ip) => write!(f, "{}", ip),
            Address::Domain(domain) => write!(f, "{}", domain),
        }
    }
}

impl SockAddr {
    /// 创建新的 SockAddr
    pub fn new(address: Address, port: u16) -> Self {
        Self { address, port }
    }

    /// 从字符串解析 SockAddr (格式: "address:port")
    pub fn parse(addr_str: &str) -> Result<Self, AddressError> {
        let parts: Vec<&str> = addr_str.rsplitn(2, ':').collect();
        if parts.len() != 2 {
            return Err(AddressError::InvalidFormat(addr_str.to_string()));
        }

        let port_str = parts[0];
        let addr_str = parts[1];

        let port: u16 = port_str
            .parse()
            .map_err(|_| AddressError::InvalidFormat(format!("Invalid port: {}", port_str)))?;

        let address = Address::parse(addr_str)?;

        Ok(SockAddr::new(address, port))
    }

    /// to_bytes
    pub fn to_bytes(&self) -> Bytes {
        // socks5 地址类型
        let mut bytes = BytesMut::with_capacity(1024);
        match &self.address {
            Address::Ipv4(ip) => {
                // SOCKS5 地址类型
                bytes.reserve(7);
                bytes.put_u8(0x01);
                bytes.put_slice(ip.octets().as_ref());
            }
            Address::Ipv6(ip) => {
                bytes.reserve(19);
                bytes.put_u8(0x04);
                bytes.put_slice(ip.octets().as_ref());
            }
            Address::Domain(domain) => {
                bytes.reserve(domain.len() + 3);
                bytes.put_u8(0x03);
                bytes.put_u8(domain.len() as u8);
                bytes.put_slice(domain.as_bytes());
            }
        }
        bytes.put_u16(self.port);
        bytes.freeze()
    }

    /// 从 SOCKS5 格式的字节流解析地址
    pub fn from_socks5(data: &mut BytesMut) -> Result<Self, AddressError> {
        use bytes::Buf;

        if data.remaining() < 1 {
            return Err(AddressError::InvalidFormat("No address type".to_string()));
        }

        let atyp = data.get_u8();

        let address = match atyp {
            1 => {
                // IPv4
                if data.remaining() < 4 {
                    return Err(AddressError::InvalidFormat("Invalid IPv4 data".to_string()));
                }
                let ip = Ipv4Addr::new(data.get_u8(), data.get_u8(), data.get_u8(), data.get_u8());
                Address::Ipv4(ip)
            }
            3 => {
                // Domain
                if data.remaining() < 1 {
                    return Err(AddressError::InvalidFormat("No domain length".to_string()));
                }
                let len = data.get_u8() as usize;
                if data.remaining() < len {
                    return Err(AddressError::InvalidFormat(
                        "Invalid domain data".to_string(),
                    ));
                }
                let domain_bytes = data.split_to(len);
                let domain = String::from_utf8(domain_bytes.to_vec())
                    .map_err(|_| AddressError::InvalidDomain("Invalid UTF-8".to_string()))?;
                Address::Domain(domain)
            }
            4 => {
                // IPv6
                if data.remaining() < 16 {
                    return Err(AddressError::InvalidFormat("Invalid IPv6 data".to_string()));
                }
                let mut octets = [0u8; 16];
                data.copy_to_slice(&mut octets);
                let ip = Ipv6Addr::from(octets);
                Address::Ipv6(ip)
            }
            _ => {
                return Err(AddressError::InvalidFormat(format!(
                    "Unknown address type: {}",
                    atyp
                )));
            }
        };

        if data.remaining() < 2 {
            return Err(AddressError::InvalidFormat("No port data".to_string()));
        }
        let port = data.get_u16();

        Ok(SockAddr::new(address, port))
    }

    /// 从 SING-BOX UOT SOCKS5 格式的字节流解析地址
    pub fn from_sing_box_uot_socks5(data: &mut BytesMut) -> Result<Self, AddressError> {
        let atyp = data.get_u8();
        let address = match atyp {
            0x00 => {
                let ip = Ipv4Addr::new(data.get_u8(), data.get_u8(), data.get_u8(), data.get_u8());
                Address::Ipv4(ip)
            }
            0x01 => {
                let mut octets = [0u8; 16];
                data.copy_to_slice(&mut octets);
                let ip = Ipv6Addr::from(octets);
                Address::Ipv6(ip)
            }
            0x02 => {
                let len = data.get_u8() as usize;
                let domain_bytes = data.split_to(len);
                let domain = String::from_utf8(domain_bytes.to_vec())
                    .map_err(|_| AddressError::InvalidDomain("Invalid UTF-8".to_string()))?;
                Address::Domain(domain)
            }
            _ => {
                return Err(AddressError::InvalidFormat(format!(
                    "Unknown address type: {}",
                    atyp
                )));
            }
        };
        Ok(SockAddr::new(address, data.get_u16()))
    }

    /// 将地址写入 SOCKS5 格式的字节流
    pub fn to_socks5(&self, buf: &mut BytesMut) -> Result<(), AddressError> {
        match &self.address {
            Address::Ipv4(ip) => {
                buf.put_u8(1); // ATYP IPv4
                buf.put_slice(&ip.octets());
            }
            Address::Ipv6(ip) => {
                buf.put_u8(4); // ATYP IPv6
                buf.put_slice(&ip.octets());
            }
            Address::Domain(domain) => {
                if domain.len() > 255 {
                    return Err(AddressError::InvalidDomain("Domain too long".to_string()));
                }
                buf.put_u8(3);
                buf.put_u8(domain.len() as u8);
                buf.put_slice(domain.as_bytes());
            }
        }
        buf.put_u16(self.port);
        Ok(())
    }

    pub fn to_sing_box_uot_socks5(&self, buf: &mut BytesMut) -> Result<(), AddressError> {
        match &self.address {
            Address::Ipv4(ip) => {
                buf.put_u8(0x00); // ATYP IPv4
                buf.put_slice(&ip.octets());
            }
            Address::Ipv6(ip) => {
                buf.put_u8(0x01); // ATYP IPv6
                buf.put_slice(&ip.octets());
            }
            Address::Domain(domain) => {
                buf.put_u8(0x02); // ATYP Domain
                buf.put_u8(domain.len() as u8);
                buf.put_slice(domain.as_bytes());
            }
        }
        buf.put_u16(self.port);
        Ok(())
    }

    /// 转换为 SOCKS5 格式的字节数组 (用于 UDP)
    pub fn into_socks5_bytes(&self) -> Result<SockAddr, AddressError> {
        // 对于 UDP 模式，我们只是返回自身
        Ok(self.clone())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_address_parse_ipv4() {
        let addr = Address::parse("***********").unwrap();
        assert!(matches!(addr, Address::Ipv4(_)));
        assert_eq!(addr.to_string(), "***********");
        assert!(addr.is_ip());
        assert!(!addr.is_domain());
    }

    #[test]
    fn test_address_parse_ipv6() {
        let addr = Address::parse("2001:db8::1").unwrap();
        assert!(matches!(addr, Address::Ipv6(_)));
        assert_eq!(addr.to_string(), "2001:db8::1");
        assert!(addr.is_ip());
        assert!(!addr.is_domain());
    }

    #[test]
    fn test_address_parse_domain() {
        let addr = Address::parse("example.com").unwrap();
        assert!(matches!(addr, Address::Domain(_)));
        assert_eq!(addr.to_string(), "example.com");
        assert!(!addr.is_ip());
        assert!(addr.is_domain());
    }

    #[test]
    fn test_address_parse_subdomain() {
        let addr = Address::parse("api.example.com").unwrap();
        assert!(matches!(addr, Address::Domain(_)));
        assert_eq!(addr.to_string(), "api.example.com");
    }

    #[test]
    fn test_address_parse_invalid_domain() {
        assert!(Address::parse("").is_err());
        assert!(Address::parse("-example.com").is_err());
        assert!(Address::parse("example-.com").is_err());
        assert!(Address::parse("example..com").is_err());
    }

    #[test]
    fn test_sockaddr_parse_ipv4() {
        let sockaddr = SockAddr::parse("***********:8080").unwrap();
        assert!(matches!(sockaddr.address, Address::Ipv4(_)));
        assert_eq!(sockaddr.port, 8080);
        assert_eq!(
            sockaddr,
            SockAddr::new(Address::Ipv4(Ipv4Addr::new(192, 168, 1, 1)), 8080)
        );
    }

    #[test]
    fn test_sockaddr_parse_ipv6() {
        let sockaddr = SockAddr::parse("[2001:db8::1]:8080").unwrap();
        assert!(matches!(sockaddr.address, Address::Ipv6(_)));
        assert_eq!(sockaddr.port, 8080);
        assert_eq!(
            sockaddr,
            SockAddr::new(
                Address::Ipv6(Ipv6Addr::new(
                    0x20, 0x01, 0xdb, 0x08, 0x00, 0x00, 0x00, 0x01
                )),
                8080
            )
        );
    }

    #[test]
    fn test_sockaddr_parse_domain() {
        let sockaddr = SockAddr::parse("example.com:443").unwrap();
        assert!(matches!(sockaddr.address, Address::Domain(_)));
        assert_eq!(sockaddr.port, 443);
        assert_eq!(
            sockaddr,
            SockAddr::new(Address::Domain("example.com".to_string()), 443)
        );
    }

    #[test]
    fn test_sockaddr_parse_invalid() {
        assert!(SockAddr::parse("invalid").is_err());
        assert!(SockAddr::parse("example.com").is_err());
        assert!(SockAddr::parse("example.com:99999").is_err());
    }

    #[test]
    fn test_sockaddr_new() {
        let addr = Address::Domain("test.com".to_string());
        let sockaddr = SockAddr::new(addr, 80);
        assert_eq!(sockaddr.port, 80);
        assert_eq!(
            sockaddr,
            SockAddr::new(Address::Domain("test.com".to_string()), 80)
        );
    }

    #[test]
    fn test_sockaddr_to_bytes_domain() {
        let sockaddr = SockAddr::new(Address::Domain("example.com".to_string()), 80);
        let bytes = sockaddr.to_bytes();

        // 验证SOCKS5格式：类型(1) + 长度(1) + 域名(11) + 端口(2) = 15字节
        assert_eq!(bytes.len(), 15);
        assert_eq!(bytes[0], 0x03); // 域名类型
        assert_eq!(bytes[1], 11); // 域名长度
        assert_eq!(&bytes[2..13], b"example.com"); // 域名内容
        assert_eq!(u16::from_be_bytes([bytes[13], bytes[14]]), 80); // 端口
    }

    #[test]
    fn test_sockaddr_to_bytes_ipv4() {
        let sockaddr = SockAddr::new(Address::Ipv4(Ipv4Addr::new(192, 168, 1, 1)), 8080);
        let bytes = sockaddr.to_bytes();

        // 验证SOCKS5格式：类型(1) + IP(4) + 端口(2) = 7字节
        assert_eq!(bytes.len(), 7);
        assert_eq!(bytes[0], 0x01); // IPv4类型
        assert_eq!(&bytes[1..5], &[192, 168, 1, 1]); // IP地址
        assert_eq!(u16::from_be_bytes([bytes[5], bytes[6]]), 8080); // 端口
    }
}
