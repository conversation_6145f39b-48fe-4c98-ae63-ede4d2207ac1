use crate::protocol::command::Command;
use crate::protocol::frame::FRAME_HEADER_SIZE;
use bytes::{BufMut, Bytes, BytesMut};
use md5;
use std::collections::HashMap;
use std::fmt::{self, Display};
use std::num::ParseIntError;
use std::string::FromUtf8Error;
use thiserror::Error;
use tokio::io::AsyncWriteExt;
use tokio::io::{self, AsyncWrite};
pub const CHECK_MARK: isize = -1;

#[derive(Debug, Error)]
pub enum PaddingError {
    #[error("UTF-8 conversion error: {0}")]
    Utf8Error(#[from] FromUtf8Error),
    #[error("Integer parsing error: {0}")]
    ParseIntError(#[from] ParseIntError),
    #[error("Invalid format: {0}")]
    InvalidFormat(String),
}

#[derive(Debug, Clone)]
pub struct PaddingScheme {
    pub stop: u8,
    pub schemes: HashMap<u8, PaddingRule>,
    md5_hash: String,
}

#[derive(Debug, Clone)]
pub struct PaddingRule {
    pub segments: Vec<PaddingSegment>,
}

impl PaddingRule {
    fn parse(value: &str) -> Result<Self, PaddingError> {
        let mut segments: Vec<PaddingSegment> = Vec::new();

        // 按逗号分割段
        for segment_str in value.split(',') {
            let segment_str = segment_str.trim();

            // 检查是否有 'c' 标志
            if segment_str == "c" {
                // 如果遇到单独的 'c'，将其应用到上一个段
                if let Some(last_segment) = segments.last_mut() {
                    last_segment.check_remaining = true;
                }
                continue;
            }

            // 解析范围格式 "min-max"
            if let Some(dash_pos) = segment_str.find('-') {
                let min_str = &segment_str[..dash_pos];
                let max_str = &segment_str[dash_pos + 1..];

                let min_size: u16 = min_str.parse()?;
                let max_size: u16 = max_str.parse()?;

                segments.push(PaddingSegment {
                    min_size,
                    max_size,
                    check_remaining: false,
                });
            } else {
                return Err(PaddingError::InvalidFormat(format!(
                    "Invalid segment format: {}",
                    segment_str
                )));
            }
        }

        Ok(PaddingRule { segments })
    }
}

#[derive(Debug, Clone)]
pub struct PaddingSegment {
    pub min_size: u16,
    pub max_size: u16,
    pub check_remaining: bool, // 'c' 标志
}

impl Default for PaddingScheme {
    fn default() -> Self {
        let mut schemes = HashMap::new();

        // 实现默认的填充方案
        schemes.insert(
            0,
            PaddingRule {
                segments: vec![PaddingSegment {
                    min_size: 30,
                    max_size: 30,
                    check_remaining: false,
                }],
            },
        );

        schemes.insert(
            1,
            PaddingRule {
                segments: vec![PaddingSegment {
                    min_size: 100,
                    max_size: 400,
                    check_remaining: false,
                }],
            },
        );

        schemes.insert(
            2,
            PaddingRule {
                segments: vec![
                    PaddingSegment {
                        min_size: 400,
                        max_size: 500,
                        check_remaining: true,
                    },
                    PaddingSegment {
                        min_size: 500,
                        max_size: 1000,
                        check_remaining: true,
                    },
                    PaddingSegment {
                        min_size: 500,
                        max_size: 1000,
                        check_remaining: true,
                    },
                    PaddingSegment {
                        min_size: 500,
                        max_size: 1000,
                        check_remaining: true,
                    },
                    PaddingSegment {
                        min_size: 500,
                        max_size: 1000,
                        check_remaining: false,
                    },
                ],
            },
        );

        //3: 9-9,500-1000
        schemes.insert(
            3,
            PaddingRule {
                segments: vec![
                    PaddingSegment {
                        min_size: 9,
                        max_size: 9,
                        check_remaining: false,
                    },
                    PaddingSegment {
                        min_size: 500,
                        max_size: 1000,
                        check_remaining: false,
                    },
                ],
            },
        );

        //4=500-1000
        schemes.insert(
            4,
            PaddingRule {
                segments: vec![PaddingSegment {
                    min_size: 500,
                    max_size: 1000,
                    check_remaining: false,
                }],
            },
        );

        //5=500-1000
        schemes.insert(
            5,
            PaddingRule {
                segments: vec![PaddingSegment {
                    min_size: 500,
                    max_size: 1000,
                    check_remaining: false,
                }],
            },
        );

        //6=500-1000
        schemes.insert(
            6,
            PaddingRule {
                segments: vec![PaddingSegment {
                    min_size: 500,
                    max_size: 1000,
                    check_remaining: false,
                }],
            },
        );

        //7=500-1000
        schemes.insert(
            7,
            PaddingRule {
                segments: vec![PaddingSegment {
                    min_size: 500,
                    max_size: 1000,
                    check_remaining: false,
                }],
            },
        );

        let mut instance = Self { 
            stop: 8, 
            schemes,
            md5_hash: String::new(),
        };
        instance.update_md5();
        instance
    }
}

impl Display for PaddingScheme {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        // 添加 stop 行
        writeln!(f, "stop={}", self.stop)?;

        // 按键排序以保证输出的一致性
        let mut keys: Vec<u8> = self.schemes.keys().cloned().collect();
        keys.sort();

        for key in keys {
            if let Some(rule) = self.schemes.get(&key) {
                write!(f, "{}=", key)?;

                for (i, segment) in rule.segments.iter().enumerate() {
                    if i > 0 {
                        write!(f, ",")?;
                    }

                    // 写入范围 - 始终使用 min-max 格式以确保解析器兼容
                    write!(f, "{}-{}", segment.min_size, segment.max_size)?;

                    // 如果有 check_remaining 标志，添加 'c'
                    if segment.check_remaining {
                        write!(f, ",c")?;
                    }
                }

                writeln!(f)?;
            }
        }

        Ok(())
    }
}

impl PaddingScheme {
    pub fn parse(data: &[u8]) -> Result<Self, PaddingError> {
        let content = String::from_utf8(data.to_vec())?;
        let mut scheme = PaddingScheme {
            stop: 8,
            schemes: HashMap::new(),
            md5_hash: String::new(),
        };

        for line in content.lines() {
            if let Some(stripped) = line.strip_prefix("stop=") {
                scheme.stop = stripped.parse()?;
            } else if let Some(equals_pos) = line.find('=') {
                let key: u8 = line[..equals_pos].parse()?;
                let value = &line[equals_pos + 1..];

                let rule = PaddingRule::parse(value)?;
                scheme.schemes.insert(key, rule);
            }
        }

        scheme.update_md5();
        Ok(scheme)
    }

    /// 更新内部存储的 MD5 哈希值
    fn update_md5(&mut self) {
        let scheme_string = self.to_string();
        let digest = md5::compute(scheme_string.as_bytes());
        self.md5_hash = format!("{:x}", digest);
    }

    /// 获取缓存的 MD5 哈希值
    pub fn get_md5(&self) -> &str {
        &self.md5_hash
    }

    /// 从字符串直接计算 MD5 哈希值
    pub fn string_to_md5(input: &str) -> String {
        let digest = md5::compute(input.as_bytes());
        format!("{:x}", digest)
    }

    /// 更新填充方案并重新计算 MD5
    pub fn update_scheme(&mut self, key: u8, rule: PaddingRule) {
        self.schemes.insert(key, rule);
        self.update_md5();
    }

    /// 设置停止值并重新计算 MD5
    pub fn set_stop(&mut self, stop: u8) {
        self.stop = stop;
        self.update_md5();
    }

    pub fn generate_record_payload_sizes(&self, pkt_index: u32) -> Vec<isize> {
        // 如果pkt_index大于stop，则返回空
        if pkt_index > self.stop as u32 {
            return Vec::new();
        }
        let mut pkt_sizes = Vec::new();
        if let Some(rule) = self.schemes.get(&(pkt_index as u8)) {
            let mut rng = fastrand::Rng::new();
            for segment in &rule.segments {
                let size = if segment.min_size == segment.max_size {
                    segment.min_size as isize
                } else {
                    rng.u16(segment.min_size..=segment.max_size) as isize
                };
                pkt_sizes.push(size);
                if segment.check_remaining {
                    pkt_sizes.push(CHECK_MARK);
                }
            }
        }
        pkt_sizes
    }
}

pub struct PaddingWriter<W> {
    packet_index: u8,
    padding_scheme: PaddingScheme,
    writer: W,
    write_buffer: BytesMut,
}

impl<W> PaddingWriter<W>
where
    W: AsyncWrite + Unpin,
{
    pub fn new(padding_scheme: PaddingScheme, writer: W) -> Self {
        Self {
            packet_index: 1,
            padding_scheme,
            writer,
            write_buffer: BytesMut::with_capacity(16 * 1024), // 预分配4KB缓冲区
        }
    }

    pub fn get_padding_scheme(&self) -> PaddingScheme {
        self.padding_scheme.clone()
    }

    pub async fn write(&mut self, mut data: Bytes) -> io::Result<()> {
        let pkt = self.packet_index;

        if pkt > self.padding_scheme.stop {
            if !data.is_empty() {
                self.writer.write_all(&data).await?
            }
            self.writer.flush().await?;
            return Ok(());
        }

        let pkt_sizes = self
            .padding_scheme
            .generate_record_payload_sizes(pkt as u32);

        for &l in &pkt_sizes {
            let remain_payload_len = data.len();
            if l == CHECK_MARK {
                if remain_payload_len == 0 {
                    break;
                } else {
                    continue;
                }
            }

            let l_usize = l as usize;

            if remain_payload_len > l_usize {
                self.writer.write_all(&data[..l_usize]).await?;
                data = data.slice(l_usize..);
            } else if remain_payload_len > 0 {
                let padding_len =
                    l_usize as isize - remain_payload_len as isize - FRAME_HEADER_SIZE as isize;
                if padding_len > 0 {
                    let padding_len = padding_len as usize;
                    // 复用缓冲区，避免重复分配
                    self.write_buffer.clear();
                    self.write_buffer
                        .reserve(remain_payload_len + FRAME_HEADER_SIZE + padding_len);

                    self.write_buffer.put_slice(&data);
                    self.write_buffer.put_u8(Command::Waste as u8);
                    self.write_buffer.put_u32(0);
                    self.write_buffer.put_u16(padding_len as u16);
                    self.write_buffer
                        .resize(self.write_buffer.len() + padding_len, 0);

                    self.writer.write_all(&self.write_buffer).await?
                } else {
                    self.writer.write_all(&data).await?
                }
                data = Bytes::new();
            } else {
                // 复用缓冲区生成padding
                self.write_buffer.clear();
                self.write_buffer.reserve(FRAME_HEADER_SIZE + l_usize);

                self.write_buffer.put_u8(Command::Waste as u8);
                self.write_buffer.put_u32(0);
                self.write_buffer.put_u16(l_usize as u16);
                self.write_buffer
                    .resize(self.write_buffer.len() + l_usize, 0);

                self.writer.write_all(&self.write_buffer).await?
            }
        }

        if !data.is_empty() {
            self.writer.write_all(&data).await?
        }
        self.writer.flush().await?;
        self.packet_index += 1;
        Ok(())
    }

    pub fn update_padding_scheme(&mut self, padding_scheme: PaddingScheme) {
        self.padding_scheme = padding_scheme;
    }
    // 返回指定packet_index的padding scheme
    pub fn get_padding_data(&self, packet_index: u8) -> Bytes {
        let padding_scheme = self
            .padding_scheme
            .generate_record_payload_sizes(packet_index as u32);
        if padding_scheme.is_empty() {
            return Bytes::new();
        }
        let mut padding_data = BytesMut::new();
        if padding_scheme.len() == 1 {
            padding_data.put_u16(padding_scheme[0] as u16);
            padding_data.resize(padding_data.len() + padding_scheme[0] as usize, 0);
        } else {
            padding_data.put_u16(30);
            padding_data.resize(padding_data.len() + 30, 0);
        }
        padding_data.into()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_parse_padding_scheme() {
        let test_data = r#"stop=8
0=30-30
1=100-400
2=400-500,c,500-1000,c,500-1000,c,500-1000,c,500-1000
3=9-9,500-1000
4=500-1000
5=500-1000
6=500-1000
7=500-1000"#;

        let scheme = PaddingScheme::parse(test_data.as_bytes()).unwrap();

        assert_eq!(scheme.stop, 8);

        // 测试规则 0
        let rule0 = scheme.schemes.get(&0).unwrap();
        assert_eq!(rule0.segments.len(), 1);
        assert_eq!(rule0.segments[0].min_size, 30);
        assert_eq!(rule0.segments[0].max_size, 30);
        assert!(!rule0.segments[0].check_remaining);

        // 测试规则 2 (包含 'c' 标志)
        let rule2 = scheme.schemes.get(&2).unwrap();
        assert_eq!(rule2.segments.len(), 5);
        assert_eq!(rule2.segments[0].min_size, 400);
        assert_eq!(rule2.segments[0].max_size, 500);
        assert!(rule2.segments[0].check_remaining);
        assert!(rule2.segments[1].check_remaining);
        assert!(!rule2.segments[4].check_remaining);

        // 测试规则 3 (混合格式)
        let rule3 = scheme.schemes.get(&3).unwrap();
        assert_eq!(rule3.segments.len(), 2);
        assert_eq!(rule3.segments[0].min_size, 9);
        assert_eq!(rule3.segments[0].max_size, 9);
        assert_eq!(rule3.segments[1].min_size, 500);
        assert_eq!(rule3.segments[1].max_size, 1000);
    }

    #[test]
    fn test_padding_scheme_to_string() {
        let test_data = r#"stop=8
0=30-30
1=100-400
2=400-500,c,500-1000,c,500-1000,c,500-1000,c,500-1000
3=9-9,500-1000"#;

        let scheme = PaddingScheme::parse(test_data.as_bytes()).unwrap();
        let serialized = scheme.to_string();

        // 验证可以往返转换
        let reparsed_scheme = PaddingScheme::parse(serialized.as_bytes()).unwrap();
        assert_eq!(scheme.stop, reparsed_scheme.stop);
        assert_eq!(scheme.schemes.len(), reparsed_scheme.schemes.len());

        // 验证 MD5 功能
        let md5_hash = scheme.get_md5();
        assert_eq!(md5_hash.len(), 32); // MD5 哈希应该是32个字符

        // 测试字符串直接转 MD5
        let direct_md5 = PaddingScheme::string_to_md5(test_data);
        assert_eq!(direct_md5.len(), 32);
    }

    #[test]
    fn test_md5_consistency() {
        let scheme = PaddingScheme::default();
        let md5_1 = scheme.get_md5();
        let md5_2 = scheme.get_md5();

        // 同一个 scheme 的 MD5 应该一致
        assert_eq!(md5_1, md5_2);

        // 序列化后再解析的 scheme 应该有相同的 MD5
        let serialized = scheme.to_string();
        let reparsed = PaddingScheme::parse(serialized.as_bytes()).unwrap();
        let md5_3 = reparsed.get_md5();
        assert_eq!(md5_1, md5_3);
    }

    #[test]
    fn test_md5_update_on_change() {
        let mut scheme = PaddingScheme::default();
        let original_md5 = scheme.get_md5().to_string();

        // 修改 stop 值应该更新 MD5
        scheme.set_stop(10);
        let new_md5 = scheme.get_md5();
        assert_ne!(original_md5, new_md5);

        // 添加新规则应该更新 MD5
        let old_md5 = new_md5.to_string();
        scheme.update_scheme(9, PaddingRule {
            segments: vec![PaddingSegment {
                min_size: 100,
                max_size: 200,
                check_remaining: false,
            }],
        });
        assert_ne!(old_md5, scheme.get_md5());
    }
}
