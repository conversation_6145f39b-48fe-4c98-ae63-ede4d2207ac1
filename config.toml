# BOA SOCKS5 Proxy Server Configuration File
# This is an example configuration file showing all available options

# Global log level - controls verbosity of logging output
# Valid values: "error", "warn", "info", "debug", "trace"
log_level = "info"

[client]
# Password for authenticating with the remote server
password = "Gg980425=="

# Remote server address (host:port format)
remote_address = "us.goopq.com:25565"

# Interval in seconds between idle session checks
idle_session_check_interval = 30

# Timeout in seconds for idle sessions before they are closed
idle_session_timeout = 60

# Minimum number of idle sessions to maintain in the pool
min_idle_session = 4

# Local IP address to bind the SOCKS5 server to
# Use "0.0.0.0" to listen on all interfaces
# Use "127.0.0.1" to listen only on localhost
listen_address = "127.0.0.1"

# Local port for the SOCKS5 server to listen on
listen_port = 1080

# SNI (Server Name Indication) hostname for TLS connections
# This should match the hostname in the server's certificate
sni_hostname = "us.goopq.com"

# Skip TLS certificate verification (INSECURE - use only for testing)
# Set to true to disable certificate validation
skip_verify = true
