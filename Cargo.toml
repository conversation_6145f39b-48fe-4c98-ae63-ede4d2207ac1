[package]
name = "boa"
version = "0.1.0"
edition = "2024"

[dependencies]
bytes = "1.10.1"
fastrand = "2.3.0"
md5 = "0.7.0"
thiserror = "2.0.12"
parking_lot = "0.12.1"
tracing-subscriber = "0.3.19"
tracing = "0.1.41"
tokio = { version = "1.45.1", features = ["full"] }
tokio-stream = "0.1.14"
tokio-rustls = "0.26.2"
tokio-util = { version = "0.7.12", features = ["time"] }
rustls = "0.23.27"
webpki-roots = "1.0.0"
sha2 = "0.10.9"
mimalloc = "0.1.46"
dhat = "0.3.3"
object-pool = "0.6.0"
smallvec = "1.15.1"
crossbeam = "0.8.4"

[dev-dependencies]
criterion = { version = "0.5", features = ["html_reports"] }

# 发布模式性能优化
[profile.release]
debug = 1

# 开发模式适中优化
[profile.dev]
opt-level = 1
debug = true
